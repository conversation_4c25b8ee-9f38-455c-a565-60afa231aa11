import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useQueryClient } from "@tanstack/react-query";
import { useLanguage } from "./useLanguage";
import { User } from "./useSharedData";
import { useAuth } from "./useAuth";

export const useUserOperations = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  const deleteUser = async (userId: string) => {
    if (!isAuthenticated) {
      toast(t("sessionExpired") || "انتهت الجلسة", {
        description: t("pleaseLogin") || "الرجاء تسجيل الدخول مرة أخرى"
      });
      return false;
    }
    
    try {

      // Delete the user from the users table first
      const { error: dbError } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);
      
      if (dbError) {

        throw new Error(`Failed to delete user from database: ${dbError.message}`);
      }

      // Use the RPC function to delete the user from auth system
      const { error: authError } = await supabase
        .rpc('delete_auth_user', { user_id: userId });
      
      if (authError) {

      } else {

      }
      
      toast(t("deleteSuccess"), {
        description: t("deleteUserSuccess")
      });
      
      queryClient.invalidateQueries({ queryKey: ['users'] });
      return true;
    } catch (error) {

      toast("Error", {
        description: error instanceof Error ? error.message : "Failed to delete user"
      });
      return false;
    }
  };

  const updateUser = async (updatedUser: User) => {
    if (!isAuthenticated) {
      toast(t("sessionExpired") || "انتهت الجلسة", {
        description: t("pleaseLogin") || "الرجاء تسجيل الدخول مرة أخرى"
      });
      return false;
    }
    
    try {
      const { error } = await supabase
        .from('users')
        .update({
          name: updatedUser.Name,
          email: updatedUser.Email,
          password: updatedUser.Password,
          phone: updatedUser.Phone,
          country: updatedUser.Country,
          activate: updatedUser.Activate,
          block: updatedUser.Block,
        })
        .eq('id', updatedUser.id);

      if (error) {
        throw new Error("Failed to update user");
      }

      toast(t("updateSuccess"), {
        description: t("updateUserSuccess")
      });
      
      queryClient.invalidateQueries({ queryKey: ['users'] });
      return true;
    } catch (error) {

      toast("Error", {
        description: "Failed to update user data"
      });
      return false;
    }
  };

  const addUser = async (newUser: any) => {
    if (!isAuthenticated) {
      toast(t("sessionExpired") || "انتهت الجلسة", {
        description: t("pleaseLogin") || "الرجاء تسجيل الدخول مرة أخرى"
      });
      return false;
    }
    
    try {

      // التحقق من وجود المستخدم في جدول users العام
      const { data: existingUserData, error: userCheckError } = await supabase
        .from('users')
        .select('id, email')
        .eq('email', newUser.Email)
        .maybeSingle();
      
      if (userCheckError) {

        throw new Error("Failed to check existing users");
      }
      
      if (existingUserData) {
        throw new Error("User already exists in the system");
      }
      
      // إنشاء مستخدم جديد أو الحصول على معرف المستخدم الموجود

      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        throw new Error("No active session found");
      }
      
      const response = await fetch(`https://sxigocnatqgqgiedrgue.supabase.co/functions/v1/create-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.session.access_token}`,
        },
        body: JSON.stringify({
          email: newUser.Email,
          password: newUser.Password,
          name: newUser.Name || ''
        })
      });
      
      const result = await response.json();
      
      if (!result.success) {

        throw new Error(result.error || "Failed to create/check auth user");
      }
      
      const userId = result.userId;
      const userExistsInAuth = result.userExistsInAuth;
      
      if (userExistsInAuth) {

      } else {

      }
      
      // إضافة بيانات المستخدم في public.users
      const { error: userError } = await supabase.from('users').insert({
        id: userId,
        uid: userId,
        email: newUser.Email,
        password: newUser.Password,
        name: newUser.Name || '',
        phone: newUser.Phone || '',
        country: newUser.Country,
        activate: 'Active',
        block: newUser.Block || 'Not Blocked',
        credits: newUser.Credits || '0.0',
        user_type: newUser.User_Type,
        email_type: 'User',
        expiry_time: newUser.Expiry_Time,
        start_date: new Date().toISOString().split('T')[0],
        hwid: 'Null'
      });

      if (userError) {

        throw new Error("Failed to add user data: " + userError.message);
      }

      toast(t("addSuccess"), {
        description: userExistsInAuth ? 
          "تم إضافة بيانات المستخدم بنجاح (المستخدم موجود مسبقاً في النظام)" : 
          t("addUserSuccess")
      });
      
      queryClient.invalidateQueries({ queryKey: ['users'] });
      return true;
    } catch (error) {

      toast(t("error") || "Error", {
        description: error instanceof Error ? error.message : "Failed to add user"
      });
      return false;
    }
  };

  const renewUser = async (user: User, months: string) => {
    if (!user) return false;
    
    if (!isAuthenticated) {
      toast(t("sessionExpired") || "انتهت الجلسة", {
        description: t("pleaseLogin") || "الرجاء تسجيل الدخول مرة أخرى"
      });
      return false;
    }
    
    try {
      const expiryDate = new Date();
      expiryDate.setMonth(expiryDate.getMonth() + parseInt(months));
      const newExpiryDate = expiryDate.toISOString().split('T')[0];
      
      const { error } = await supabase
        .from('users')
        .update({
          user_type: "Monthly License",
          expiry_time: newExpiryDate
        })
        .eq('id', user.id);

      if (error) {
        throw new Error("Failed to renew user");
      }

      toast(t("renewSuccess"), {
        description: t("renewUserSuccess")
      });
      
      queryClient.invalidateQueries({ queryKey: ['users'] });
      return true;
    } catch (error) {

      toast("Error", {
        description: "Failed to renew user account"
      });
      return false;
    }
  };

  const addPlanToUser = async (userId: string, planName: string, duration: number) => {
    if (!isAuthenticated) {
      toast(t("sessionExpired") || "انتهت الجلسة", {
        description: t("pleaseLogin") || "الرجاء تسجيل الدخول مرة أخرى"
      });
      return false;
    }
    
    try {

      // Get the user's current information
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('expiry_time, my_plans')
        .eq('id', userId)
        .single();
      
      if (userError) {

        throw new Error("Failed to fetch user data");
      }
      
      // Calculate the new expiry date
      let newExpiryDate;
      const currentDate = new Date();
      
      if (userData.expiry_time) {
        // If there's an existing expiry date, check if it's in the future
        const currentExpiryDate = new Date(userData.expiry_time);
        if (currentExpiryDate > currentDate) {
          // If the current expiry date is in the future, add the months to it
          currentExpiryDate.setMonth(currentExpiryDate.getMonth() + duration);
          newExpiryDate = currentExpiryDate.toISOString().split('T')[0];
        } else {
          // If the current expiry date is in the past, add months to today
          currentDate.setMonth(currentDate.getMonth() + duration);
          newExpiryDate = currentDate.toISOString().split('T')[0];
        }
      } else {
        // If there's no expiry date, add months to today
        currentDate.setMonth(currentDate.getMonth() + duration);
        newExpiryDate = currentDate.toISOString().split('T')[0];
      }
      
      // Add the new plan to the user's plans (if my_plans exists)
      let updatedPlans = userData.my_plans ? userData.my_plans : '';
      if (updatedPlans) {
        updatedPlans += `, ${planName}`;
      } else {
        updatedPlans = planName;
      }
      
      // Update the user with the new expiry date and plan
      const { error: updateError } = await supabase
        .from('users')
        .update({
          user_type: "Monthly License",
          expiry_time: newExpiryDate,
          my_plans: updatedPlans
        })
        .eq('id', userId);
      
      if (updateError) {

        throw new Error("Failed to add plan to user");
      }
      
      toast(t("addPlanSuccess") || "Plan Added", {
        description: t("addPlanDescription") || "Plan has been successfully added to the user"
      });
      
      queryClient.invalidateQueries({ queryKey: ['users'] });
      return true;
    } catch (error) {

      toast(t("error") || "Error", {
        description: error instanceof Error ? error.message : "Failed to add plan to user"
      });
      return false;
    }
  };

  return {
    deleteUser,
    updateUser,
    addUser,
    renewUser,
    addPlanToUser
  };
};
