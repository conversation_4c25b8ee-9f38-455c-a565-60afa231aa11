
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";

export function useGroups() {
  const { t } = useLanguage();
  const { user, isAdmin } = useAuth();
  
  return useQuery({
    queryKey: ['groups'],
    queryFn: async () => {
      if (!user || !isAdmin) {
        return [];
      }

      const { data, error } = await supabase
        .from('groups')
        .select('*');
      
      if (error) {
        toast.error(t("errorFetchingData") || "Error fetching data");
        throw error;
      }

      return data || [];
    },
    enabled: !!user && isAdmin
  });
}
