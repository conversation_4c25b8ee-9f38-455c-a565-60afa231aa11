import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSharedData, useLanguage } from "@/hooks/useSharedData";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "@/components/ui/sonner";
import { Users } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { ViewUserDialog } from "@/components/users/ViewUserDialog";
import { EditUserDialog } from "@/components/users/EditUserDialog";
import { AddUserDialog } from "@/components/users/AddUserDialog";
import { RenewUserDialog } from "@/components/users/RenewUserDialog";
import { AddCreditsDialog } from "@/components/users/AddCreditsDialog";
import { AddToPlanDialog } from "@/components/users/AddToPlanDialog";
import { UserFilters } from "@/components/users/UserFilters";
import { UserHeaderActions } from "@/components/users/UserHeaderActions";
import { useUserDialogs } from "@/hooks/useUserDialogs";
import { useUserOperations } from "@/hooks/data/useUserOperations";
import { UsersTable } from "@/components/users/UsersTable";

export default function UsersManager() {
  const navigate = useNavigate();
  const { users, isLoading, addCreditToUser, refreshData } = useSharedData();
  const { t, isRTL } = useLanguage();
  const queryClient = useQueryClient();
  const { role } = useAuth();
  const {
    selectedUser,
    isViewDialogOpen,
    isEditDialogOpen,
    isAddDialogOpen,
    isRenewDialogOpen,
    isAddCreditsDialogOpen,
    setIsViewDialogOpen,
    setIsEditDialogOpen,
    setIsAddDialogOpen,
    setIsRenewDialogOpen,
    setIsAddCreditsDialogOpen,
    openViewDialog,
    openEditDialog,
    openRenewDialog,
    openAddDialog,
    openAddCreditsDialog
  } = useUserDialogs();
  
  // Add state for the new dialog
  const [isAddToPlanDialogOpen, setIsAddToPlanDialogOpen] = useState(false);
  
  const { updateUser, addUser, renewUser, deleteUser, addPlanToUser } = useUserOperations();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredUsers, setFilteredUsers] = useState(users);
  
  // Function to handle data refreshing with enhanced logging
  const handleRefresh = () => {
    refreshData();
  };

  useEffect(() => {
    handleRefresh();
  }, []);
  
  // Function to handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // Filter users based on search query
    const filtered = users.filter(user => {
      const matchesSearch = query.trim() === "" || 
        (user.Email?.toLowerCase().includes(query.toLowerCase())) ||
        (user.Name?.toLowerCase().includes(query.toLowerCase())) ||
        (user.Phone?.toLowerCase().includes(query.toLowerCase())) ||
        (user.Country?.toLowerCase().includes(query.toLowerCase()));
      
      return matchesSearch;
    });
    
    setFilteredUsers(filtered);
  };

  // Enhanced handler with logging for adding credits
  const handleAddCreditsConfirm = async (userId: string, creditsToAdd: number) => {

    try {
      await addCreditToUser(userId, creditsToAdd);
      
      toast(t("addCreditSuccess") || "Credits Added", {
        description: t("addCreditDescription") || "Credits have been added successfully"
      });
      handleRefresh();
    } catch (error) {

      toast(t("error") || "Error", {
        description: t("failedToAddCredits") || "Failed to add credits"
      });
    }
  };

  // Enhanced handlers for user operations
  const handleUpdateUser = async (updatedUser: any) => {

    try {
      await updateUser(updatedUser);

      handleRefresh();
      return true;
    } catch (error) {

      return false;
    }
  };

  const handleAddUser = async (newUser: any) => {

    try {
      await addUser(newUser);

      handleRefresh();
      return true;
    } catch (error) {

      return false;
    }
  };

  const handleRenewUser = async (user: any, months: string) => {

    try {
      await renewUser(user, months);

      handleRefresh();
      return true;
    } catch (error) {

      return false;
    }
  };

  const handleDeleteUser = async (userId: string) => {

    if (confirm(t("confirmDelete") || "Are you sure you want to delete this user?")) {
      try {
        await deleteUser(userId);

        handleRefresh();
      } catch (error) {

      }
    }
  };

  // Handler for adding a plan to a user
  const handleAddPlanToUser = async (userId: string, planName: string, duration: number) => {

    try {
      const success = await addPlanToUser(userId, planName, duration);
      if (success) {

        handleRefresh();
      }
      return success;
    } catch (error) {

      return false;
    }
  };

  const openAddToPlanDialog = () => {
    setIsAddToPlanDialogOpen(true);
  };

  // Initialize filtered users when users change
  useEffect(() => {

    handleSearch(searchQuery);
  }, [users, searchQuery]);

  return (
    <div dir={isRTL ? "rtl" : "ltr"} className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <Users className="h-5 w-5" />
              <span>{t("users")}</span>
            </CardTitle>
            <CardDescription>
              {t("usersDescription")}
              {users.length > 0 && (
                <span className="ml-2 font-medium">
                  ({users.length} {t("totalUsers") || "total users"})
                </span>
              )}
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <UserFilters onSearch={handleSearch} />
            
            <UserHeaderActions
              onRefresh={handleRefresh}
              onAddCredits={openAddCreditsDialog}
              onAddUser={openAddDialog}
              onAddToPlan={openAddToPlanDialog}
            />
          </div>
          
          <UsersTable
            users={filteredUsers}
            isLoading={isLoading}
            onViewUser={openViewDialog}
            onEditUser={openEditDialog}
            onRenewUser={openRenewDialog}
            onDeleteUser={handleDeleteUser}
          />
        </CardContent>
      </Card>

      <ViewUserDialog 
        isOpen={isViewDialogOpen} 
        onClose={() => setIsViewDialogOpen(false)} 
        user={selectedUser} 
      />
      
      <EditUserDialog 
        isOpen={isEditDialogOpen} 
        onClose={() => setIsEditDialogOpen(false)} 
        user={selectedUser}
        onSave={handleUpdateUser}
      />
      
      <RenewUserDialog
        isOpen={isRenewDialogOpen}
        onClose={() => setIsRenewDialogOpen(false)}
        onConfirm={(months) => selectedUser && handleRenewUser(selectedUser, months)}
        userType={selectedUser?.User_Type || ""}
      />
      
      <AddUserDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        onSave={handleAddUser}
      />

      <AddCreditsDialog
        isOpen={isAddCreditsDialogOpen}
        onClose={() => setIsAddCreditsDialogOpen(false)}
        users={users}
        onAddCredits={handleAddCreditsConfirm}
      />
      
      <AddToPlanDialog
        isOpen={isAddToPlanDialogOpen}
        onClose={() => setIsAddToPlanDialogOpen(false)}
        users={users}
        onAddPlan={handleAddPlanToUser}
      />
    </div>
  );
}
