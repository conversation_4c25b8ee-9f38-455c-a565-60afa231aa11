
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";

export function useDiscounts() {
  const { t } = useLanguage();
  const { user, isAdmin } = useAuth();
  
  return useQuery({
    queryKey: ['discounts'],
    queryFn: async () => {
      if (!user || !isAdmin) {

        return [];
      }

      const { data, error } = await supabase
        .from('discounts')
        .select('*');
      
      if (error) {

        toast.error(t("errorFetchingData") || "Error fetching data");
        throw error;
      }

      return data || [];
    },
    enabled: !!user && isAdmin
  });
}

// Add hook for managing offers
export function useOffers() {
  const { t } = useLanguage();
  const { user, isAdmin } = useAuth();
  
  return useQuery({
    queryKey: ['offers'],
    queryFn: async () => {
      if (!user || !isAdmin) {

        return [];
      }

      const { data, error } = await supabase
        .from('offers')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {

        toast.error(t("errorFetchingData") || "Error fetching data");
        throw error;
      }
      
      // Process offers to check for expired status
      const currentDate = new Date();
      const processedOffers = data?.map(offer => {
        const isExpired = offer.expiry_at ? new Date(offer.expiry_at) < currentDate : false;
        return {
          ...offer,
          status: isExpired ? "expired" : (offer.status || "active")
        };
      }) || [];

      return processedOffers;
    },
    enabled: !!user && isAdmin
  });
}
