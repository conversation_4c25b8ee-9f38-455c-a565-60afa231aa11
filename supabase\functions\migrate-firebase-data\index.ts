
// This Edge Function will securely migrate data from Firebase to Supabase
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.2";

// Your Firebase and Supabase configuration
const FIREBASE_URL = Deno.env.get('FIREBASE_URL') || '';
const FIREBASE_API_KEY = Deno.env.get('FIREBASE_API_KEY') || '';
const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || 'https://sxigocnatqgqgiedrgue.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Supabase client with service role for admin operations
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false }
});

// Helper function to fetch data from Firebase
async function fetchFromFirebase(path: string, idToken: string) {
  const url = `${FIREBASE_URL}/${path}?auth=${idToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch from Firebase: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {

    throw error;
  }
}

// Helper function to sign in to Firebase
async function firebaseSignIn(email: string, password: string) {
  try {
    const response = await fetch(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${FIREBASE_API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, returnSecureToken: true }),
      }
    );
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Firebase authentication failed: ${errorData.error?.message || response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {

    throw error;
  }
}

// Main migration function
async function migrateData(adminEmail: string, adminPassword: string) {

  let stats = {
    users: { total: 0, migrated: 0, errors: 0 },
    operations: { total: 0, migrated: 0, errors: 0 }
  };
  
  try {
    // Step 1: Sign in to Firebase as admin

    const authData = await firebaseSignIn(adminEmail, adminPassword);
    const idToken = authData.idToken;
    const localId = authData.localId;
    
    if (!idToken) {
      throw new Error('Failed to get Firebase authentication token');
    }
    
    // Step 2: Fetch users data

    const usersData = await fetchFromFirebase('users.json', idToken);
    
    if (!usersData) {
      throw new Error('No users data found in Firebase');
    }
    
    // Step 3: Migrate users

    const userIds = Object.keys(usersData);
    stats.users.total = userIds.length;
    
    for (const firebaseId of userIds) {
      const userData = usersData[firebaseId];
      
      try {
        // Create auth user in Supabase (this is a simplified version, in production we'd need to handle this differently)
        const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
          email: userData.Email,
          password: userData.Password || 'tempPassword123', // You'd want a better password strategy
          email_confirm: true
        });
        
        if (authError) {

          stats.users.errors++;
          continue;
        }
        
        // Insert user data
        const { error: insertError } = await supabaseAdmin.from('users').insert({
          id: authUser.user.id,
          name: userData.Name,
          email: userData.Email,
          password: userData.Password,
          phone: userData.Phone,
          country: userData.Country,
          activate: userData.Activate,
          block: userData.Block,
          credits: userData.Credits,
          user_type: userData.User_Type,
          email_type: userData.Email_Type,
          expiry_time: userData.Expiry_Time,
          start_date: userData.Start_Date,
          hwid: userData.Hwid || 'Null',
          uid: authUser.user.id
        });
        
        if (insertError) {

          stats.users.errors++;
          continue;
        }
        
        stats.users.migrated++;
        
        // Map Firebase ID to Supabase ID for operations migration
        userData.supabaseId = authUser.user.id;
      } catch (error) {

        stats.users.errors++;
      }
    }
    
    // Step 4: Fetch operations data

    const operationsData = await fetchFromFirebase('operations.json', idToken);
    
    if (!operationsData) {

      return stats;
    }
    
    // Step 5: Migrate operations

    const operationIds = Object.keys(operationsData);
    stats.operations.total = operationIds.length;
    
    for (const firebaseOpId of operationIds) {
      const opData = operationsData[firebaseOpId];
      try {
        // Find the Supabase user ID that corresponds to this operation's UID
        const userId = opData.UID;
        const correspondingUser = Object.values(usersData).find(
          (u: any) => u.UID === userId
        ) as any;
        
        const supabaseUserId = correspondingUser?.supabaseId;
        
        if (!supabaseUserId) {

        }
        
        // Insert operation
        const { error: insertError } = await supabaseAdmin.from('operations').insert({
          id: firebaseOpId, // Using Firebase operation ID as Supabase ID
          operation_type: opData.OprationTypes,
          phone_sn: opData.Phone_SN,
          brand: opData.Brand,
          model: opData.Model,
          imei: opData.Imei,
          username: opData.UserName,
          credit: opData.Credit,
          time: opData.Time ? new Date(opData.Time) : null,
          status: opData.Status,
          android: opData.Android,
          baseband: opData.Baseband,
          carrier: opData.Carrier,
          security_patch: opData.Security_Patch,
          uid: supabaseUserId || userId,
          hwid: opData.Hwid,
          log_operation: opData.LogOpration
        });
        
        if (insertError) {

          stats.operations.errors++;
          continue;
        }
        
        stats.operations.migrated++;
      } catch (error) {

        stats.operations.errors++;
      }
    }

    return stats;
    
  } catch (error) {

    throw error;
  }
}

// Handle requests
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  // Check if service role key is available
  if (!SUPABASE_SERVICE_ROLE_KEY) {
    return new Response(
      JSON.stringify({ error: 'Service role key is required but not configured' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
  
  try {
    const { email, password } = await req.json();
    
    if (!email || !password) {
      return new Response(
        JSON.stringify({ error: 'Email and password are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const results = await migrateData(email, password);
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Migration completed',
        stats: results
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during migration'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
