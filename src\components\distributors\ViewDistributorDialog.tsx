import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/hooks/useLanguage";
import { supabase } from "@/integrations/supabase/client";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Percent, 
  Users, 
  Calendar,
  DollarSign,
  Activity,
  Network,
  Shield,
  TrendingUp
} from "lucide-react";

interface Distributor {
  id: string;
  auth_user_id: string;
  name: string;
  email: string;
  phone?: string;
  parent_distributor_id?: string;
  distributor_level: number;
  commission_percentage: string;
  region?: string;
  max_sub_distributors: number;
  can_create_distributors: boolean;
  total_users: number;
  total_commission: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface ViewDistributorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  distributor: Distributor | null;
}

interface DistributorStats {
  activeUsers: number;
  inactiveUsers: number;
  monthlyUsers: number;
  creditUsers: number;
  subDistributors: number;
  totalOperations: number;
  pendingCommissions: number;
  paidCommissions: number;
}

export function ViewDistributorDialog({ isOpen, onClose, distributor }: ViewDistributorDialogProps) {
  const { t } = useLanguage();
  const [stats, setStats] = useState<DistributorStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // Fetch detailed statistics when distributor changes
  useEffect(() => {
    if (distributor && isOpen) {
      fetchDistributorStats();
    }
  }, [distributor, isOpen]);

  const fetchDistributorStats = async () => {
    if (!distributor) return;

    try {
      setIsLoadingStats(true);

      // Fetch users statistics
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('activate, block, user_type')
        .eq('distributor_id', distributor.id);

      if (usersError) throw usersError;

      // Fetch sub-distributors count
      const { data: subDistributors, error: subDistributorsError } = await supabase
        .from('distributors')
        .select('id')
        .eq('parent_distributor_id', distributor.id);

      if (subDistributorsError) throw subDistributorsError;

      // Fetch operations count
      const { data: operations, error: operationsError } = await supabase
        .from('operations')
        .select('id')
        .in('uid', users?.map(u => u.id) || []);

      if (operationsError) throw operationsError;

      // Fetch commissions statistics
      const { data: commissions, error: commissionsError } = await supabase
        .from('distributor_commissions')
        .select('commission_amount, status')
        .eq('distributor_id', distributor.id);

      if (commissionsError) throw commissionsError;

      // Calculate statistics
      const activeUsers = users?.filter(u => u.activate === 'Active').length || 0;
      const inactiveUsers = users?.filter(u => u.activate !== 'Active').length || 0;
      const monthlyUsers = users?.filter(u => u.user_type?.includes('Monthly')).length || 0;
      const creditUsers = users?.filter(u => u.user_type?.includes('Credit')).length || 0;
      const pendingCommissions = commissions?.filter(c => c.status === 'pending').reduce((sum, c) => sum + parseFloat(c.commission_amount), 0) || 0;
      const paidCommissions = commissions?.filter(c => c.status === 'paid').reduce((sum, c) => sum + parseFloat(c.commission_amount), 0) || 0;

      setStats({
        activeUsers,
        inactiveUsers,
        monthlyUsers,
        creditUsers,
        subDistributors: subDistributors?.length || 0,
        totalOperations: operations?.length || 0,
        pendingCommissions,
        paidCommissions
      });

    } catch (error) {
      console.error('Error fetching distributor stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: string | number) => {
    return parseFloat(amount.toString()).toFixed(2);
  };

  if (!distributor) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {distributor.name}
          </DialogTitle>
          <DialogDescription>
            {t("distributorDetails") || "تفاصيل الموزع"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("basicInformation") || "المعلومات الأساسية"}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("name") || "الاسم"}:</span>
                  <span>{distributor.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("email") || "البريد الإلكتروني"}:</span>
                  <span>{distributor.email}</span>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("phone") || "الهاتف"}:</span>
                  <span>{distributor.phone || t("notProvided") || "غير محدد"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("region") || "المنطقة"}:</span>
                  <span>{distributor.region || t("notProvided") || "غير محدد"}</span>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("joinDate") || "تاريخ الانضمام"}:</span>
                  <span>{formatDate(distributor.created_at)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("status") || "الحالة"}:</span>
                  <Badge variant={distributor.is_active ? "success" : "destructive"}>
                    {distributor.is_active ? t("active") || "نشط" : t("inactive") || "غير نشط"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Distributor Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("distributorSettings") || "إعدادات الموزع"}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("level") || "المستوى"}:</span>
                  <Badge variant="outline">{distributor.distributor_level}</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Percent className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("commission") || "العمولة"}:</span>
                  <Badge variant="secondary">{distributor.commission_percentage}%</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Network className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("maxSubs") || "الحد الأقصى"}:</span>
                  <span>{distributor.max_sub_distributors}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t("canCreateSubs") || "إنشاء فرعيين"}:</span>
                  <Badge variant={distributor.can_create_distributors ? "success" : "secondary"}>
                    {distributor.can_create_distributors ? t("yes") || "نعم" : t("no") || "لا"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("statistics") || "الإحصائيات"}</CardTitle>
              <CardDescription>
                {isLoadingStats 
                  ? t("loadingStats") || "جاري تحميل الإحصائيات..."
                  : t("detailedStats") || "إحصائيات مفصلة للموزع"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="text-center py-4">
                  {t("loading") || "جاري التحميل..."}
                </div>
              ) : stats ? (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="text-center p-4 border rounded-lg">
                    <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div className="text-2xl font-bold">{distributor.total_users}</div>
                    <div className="text-sm text-muted-foreground">{t("totalUsers") || "إجمالي المستخدمين"}</div>
                    <div className="text-xs mt-1">
                      <span className="text-green-600">{stats.activeUsers} {t("active") || "نشط"}</span>
                      {" / "}
                      <span className="text-red-600">{stats.inactiveUsers} {t("inactive") || "غير نشط"}</span>
                    </div>
                  </div>

                  <div className="text-center p-4 border rounded-lg">
                    <Network className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                    <div className="text-2xl font-bold">{stats.subDistributors}</div>
                    <div className="text-sm text-muted-foreground">{t("subDistributors") || "الموزعين الفرعيين"}</div>
                    <div className="text-xs mt-1 text-muted-foreground">
                      {t("maxAllowed") || "الحد الأقصى"}: {distributor.max_sub_distributors}
                    </div>
                  </div>

                  <div className="text-center p-4 border rounded-lg">
                    <Activity className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div className="text-2xl font-bold">{stats.totalOperations}</div>
                    <div className="text-sm text-muted-foreground">{t("totalOperations") || "إجمالي العمليات"}</div>
                    <div className="text-xs mt-1">
                      <span className="text-blue-600">{stats.monthlyUsers} {t("monthly") || "شهري"}</span>
                      {" / "}
                      <span className="text-orange-600">{stats.creditUsers} {t("credit") || "رصيد"}</span>
                    </div>
                  </div>

                  <div className="text-center p-4 border rounded-lg">
                    <DollarSign className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                    <div className="text-2xl font-bold">{formatCurrency(distributor.total_commission)}</div>
                    <div className="text-sm text-muted-foreground">{t("totalCommissions") || "إجمالي العمولات"}</div>
                    <div className="text-xs mt-1">
                      <span className="text-yellow-600">{formatCurrency(stats.pendingCommissions)} {t("pending") || "معلق"}</span>
                      {" / "}
                      <span className="text-green-600">{formatCurrency(stats.paidCommissions)} {t("paid") || "مدفوع"}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  {t("noStatsAvailable") || "لا توجد إحصائيات متاحة"}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end">
            <Button onClick={onClose}>
              {t("close") || "إغلاق"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
