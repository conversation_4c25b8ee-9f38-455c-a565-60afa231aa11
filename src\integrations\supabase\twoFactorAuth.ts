// 2FA functions using custom implementation
import { supabase } from './client';
import {
  generateSecret,
  generateOTPAuthURL,
  generateQRCodeSVG,
  generateQRCodeCanvas,
  svgToDataURL,
  verifyTOTP
} from '@/utils/twoFactorAuth';

// Generate 2FA secret with custom implementation
export const generate2FASecret = async (userId: string, email: string) => {
  try {
    const secret = generateSecret();
    const otpAuthUrl = generateOTPAuthURL(email, 'Pegasus Tools', secret);

    // Try Canvas first, fallback to SVG
    let qrCodeDataUrl: string;
    try {
      qrCodeDataUrl = generateQRCodeCanvas(otpAuthUrl, 256);
    } catch (canvasError) {
      const qrCodeSVG = generateQRCodeSVG(otpAuthUrl, 256);
      qrCodeDataUrl = svgToDataURL(qrCodeSVG);
    }

    const { error } = await supabase
      .from('users')
      .update({
        otp_secret: secret,
        two_factor_enabled: false
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }

    return {
      secret,
      qrCodeDataUrl,
      otpAuthUrl
    };
  } catch (error) {
    throw error;
  }
};

export const verify2FAToken = async (userId: string, token: string) => {
  try {
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret')
      .eq('id', userId)
      .single();

    if (error || !userData?.otp_secret) {
      return false;
    }

    const isValid = await verifyTOTP(token, userData.otp_secret);

    if (isValid) {
      const { error: updateError } = await supabase
        .from('users')
        .update({ two_factor_enabled: true })
        .eq('id', userId);

      if (updateError) {
        return false;
      }
    }

    return isValid;
  } catch (error) {
    return false;
  }
};

export const validate2FAToken = async (userId: string, token: string) => {
  try {
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret, two_factor_enabled')
      .eq('id', userId)
      .single();

    if (error || !userData?.otp_secret || !userData.two_factor_enabled) {
      return false;
    }

    return await verifyTOTP(token, userData.otp_secret);
  } catch (error) {
    return false;
  }
};

export const disable2FA = async (userId: string) => {
  try {
    const { error } = await supabase
      .from('users')
      .update({
        otp_secret: null,
        two_factor_enabled: false
      })
      .eq('id', userId);

    return !error;
  } catch (error) {
    return false;
  }
};

export const saveQRCodeFile = async (userId: string, qrCodeDataUrl: string) => {
  try {
    const response = await fetch(qrCodeDataUrl);
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);

    return {
      success: true,
      url: url
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const checkBrowserCompatibility = (): { supported: boolean; missingFeatures: string[] } => {
  const missingFeatures: string[] = [];

  if (!crypto || !crypto.getRandomValues) {
    missingFeatures.push('crypto.getRandomValues');
  }

  if (!crypto || !crypto.subtle) {
    missingFeatures.push('crypto.subtle (Web Crypto API)');
  }

  if (!btoa) {
    missingFeatures.push('btoa (Base64 encoding)');
  }

  if (!URLSearchParams) {
    missingFeatures.push('URLSearchParams');
  }

  return {
    supported: missingFeatures.length === 0,
    missingFeatures
  };
};
