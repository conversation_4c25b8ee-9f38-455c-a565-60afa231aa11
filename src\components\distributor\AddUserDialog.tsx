import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLanguage } from "@/hooks/useLanguage";
import { useDistributorOperations } from "@/hooks/useDistributorOperations";
import { toast } from "@/components/ui/sonner";
import { Loader2, User, Mail, Phone, MapPin, CreditCard, Calendar } from "lucide-react";

interface AddUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  email: string;
  password: string;
  phone: string;
  country: string;
  userType: string;
  credits: string;
  expiryTime: string;
}

export function AddUserDialog({ isOpen, onClose, onSuccess }: AddUserDialogProps) {
  const { t } = useLanguage();
  const { createUser } = useDistributorOperations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    phone: "",
    country: "",
    userType: "Credit License",
    credits: "0.0",
    expiryTime: ""
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast.error(t("nameRequired") || "الاسم مطلوب");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error(t("emailRequired") || "البريد الإلكتروني مطلوب");
      return false;
    }
    if (!formData.password || formData.password.length < 8) {
      toast.error(t("passwordMinLength") || "كلمة المرور يجب أن تكون 8 أحرف على الأقل");
      return false;
    }
    return true;
  };

  const generateExpiryDate = (userType: string): string => {
    if (userType === "Monthly License") {
      const date = new Date();
      date.setMonth(date.getMonth() + 1);
      return date.toISOString().split('T')[0];
    }
    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      const expiryTime = formData.userType === "Monthly License" 
        ? (formData.expiryTime || generateExpiryDate(formData.userType))
        : formData.expiryTime;

      await createUser({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        phone: formData.phone,
        country: formData.country,
        userType: formData.userType,
        credits: formData.credits,
        expiryTime: expiryTime
      });

      onSuccess();
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        password: "",
        phone: "",
        country: "",
        userType: "Credit License",
        credits: "0.0",
        expiryTime: ""
      });

    } catch (error) {
      // Error is already handled in createUser function
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("addNewUser") || "إضافة مستخدم جديد"}</DialogTitle>
          <DialogDescription>
            {t("addUserDescription") || "إضافة مستخدم جديد وتعيينه لك"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("basicInformation") || "المعلومات الأساسية"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  <User className="inline mr-1 h-4 w-4" />
                  {t("fullName") || "الاسم الكامل"} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t("enterFullName") || "أدخل الاسم الكامل"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  <Mail className="inline mr-1 h-4 w-4" />
                  {t("email") || "البريد الإلكتروني"} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"}
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">
                  {t("password") || "كلمة المرور"} *
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder={t("enterPassword") || "أدخل كلمة المرور"}
                  minLength={8}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  <Phone className="inline mr-1 h-4 w-4" />
                  {t("phone") || "رقم الهاتف"}
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t("enterPhone") || "أدخل رقم الهاتف"}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">
                <MapPin className="inline mr-1 h-4 w-4" />
                {t("country") || "البلد"}
              </Label>
              <Input
                id="country"
                value={formData.country}
                onChange={(e) => handleInputChange('country', e.target.value)}
                placeholder={t("enterCountry") || "أدخل البلد"}
              />
            </div>
          </div>

          {/* License Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("licenseInformation") || "معلومات الترخيص"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="userType">
                  {t("userType") || "نوع المستخدم"}
                </Label>
                <Select value={formData.userType} onValueChange={(value) => handleInputChange('userType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectUserType") || "اختر نوع المستخدم"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Credit License">{t("creditLicense") || "ترخيص رصيد"}</SelectItem>
                    <SelectItem value="Monthly License">{t("monthlyLicense") || "ترخيص شهري"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="credits">
                  <CreditCard className="inline mr-1 h-4 w-4" />
                  {t("initialCredits") || "الرصيد الأولي"}
                </Label>
                <Input
                  id="credits"
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.credits}
                  onChange={(e) => handleInputChange('credits', e.target.value)}
                  placeholder="0.0"
                />
              </div>
            </div>

            {formData.userType === "Monthly License" && (
              <div className="space-y-2">
                <Label htmlFor="expiryTime">
                  <Calendar className="inline mr-1 h-4 w-4" />
                  {t("expiryDate") || "تاريخ الانتهاء"}
                </Label>
                <Input
                  id="expiryTime"
                  type="date"
                  value={formData.expiryTime}
                  onChange={(e) => handleInputChange('expiryTime', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("cancel") || "إلغاء"}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting 
                ? t("creating") || "جاري الإنشاء..." 
                : t("addUser") || "إضافة المستخدم"
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
