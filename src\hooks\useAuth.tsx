
import { supabase } from "@/integrations/supabase/client";
import { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "./useLanguage";
import { AuthChangeEvent, Session } from "@supabase/supabase-js";

export type UserRole = "admin" | "user";

export interface AuthUser {
  id: string;
  email: string;
  role: UserRole;
}

export const useAuth = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<UserRole | null>(null);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { t } = useLanguage();
  const [sessionChecked, setSessionChecked] = useState(false);
  
  const fetchUserData = useCallback(async (userId: string) => {
    try {
      // Use the new get_account_info function
      const { data, error } = await supabase.rpc('get_account_info', {
        user_id: userId
      });

      if (error || !data || data.length === 0) {
        console.error('Error fetching account info:', error);
        return null;
      }

      const accountInfo = data[0];
      const { account_type, user_data } = accountInfo;

      if (!account_type || !user_data) {
        return null;
      }

      return {
        id: userId,
        email: user_data.email || '',
        role: account_type as UserRole
      };
    } catch (err) {
      console.error('Error in fetchUserData:', err);
      return null;
    }
  }, []);

  const handleSession = useCallback(async (session: Session | null) => {
    if (!session) {

      setIsAuthenticated(false);
      setUser(null);
      setRole(null);
      return;
    }

    setIsAuthenticated(true);
    
    const userData = await fetchUserData(session.user.id);
    if (userData) {
      setUser(userData as AuthUser);
      setRole(userData.role as UserRole);
    }
  }, [fetchUserData]);
  
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;
    
    const setupAuthListener = async () => {
      try {

        setLoading(true);
        
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event: AuthChangeEvent, session) => {

            switch(event) {
              case 'SIGNED_OUT':
                // Handle logout events
                setRole(null);
                setUser(null);
                setIsAuthenticated(false);
                break;
              
              case 'SIGNED_IN':
              case 'TOKEN_REFRESHED':
              case 'USER_UPDATED':
              case 'INITIAL_SESSION':
                // Don't set loading to true here, as we'll handle with setTimeout
                if (session) {
                  setTimeout(() => {
                    handleSession(session);
                  }, 0);
                }
                break;
            }
          }
        );

        unsubscribe = () => {
          subscription.unsubscribe();
        };

        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {

        } else {
          await handleSession(session);
        }
        
      } catch (err) {

      } finally {
        setLoading(false);
        setSessionChecked(true);
      }
    };

    setupAuthListener();

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [navigate, handleSession]);

  const checkSession = useCallback(async () => {
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {

        return false;
      }
      
      if (!data.session) {

        return false;
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      if (data.session.expires_at && data.session.expires_at < currentTime) {

        return false;
      }
      
      return true;
    } catch (err) {

      return false;
    }
  }, []);
  
  const handleSessionExpired = useCallback(() => {

    setRole(null);
    setUser(null);
    setIsAuthenticated(false);
    
    if (window.location.pathname !== '/login') {
      toast(t("sessionExpired") || "Session expired", {
        description: t("pleaseLoginAgain") || "Please login again"
      });
      
      navigate('/login?sessionExpired=true');
    }
  }, [navigate, t]);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) throw error;
      
      toast(t("loginSuccess"), {
        description: t("welcomeBack")
      });
      
      navigate('/dashboard');
      return true;
    } catch (error) {

      toast(t("loginFailed"), {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      
      const isSessionValid = await checkSession();
      
      if (!isSessionValid) {

        setRole(null);
        setUser(null);
        setIsAuthenticated(false);
        
        navigate('/login?loggedOut=true');
        return true;
      }
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      setRole(null);
      setUser(null);
      setIsAuthenticated(false);
      
      toast(t("logoutSuccess"), {
        description: t("comeBackSoon")
      });
      
      navigate('/login?loggedOut=true');
      return true;
    } catch (error) {

      toast(t("logoutFailed"), {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const authChannel = new BroadcastChannel('auth_state_channel');
    
    authChannel.onmessage = (event) => {
      if (event.data === 'SIGNED_OUT') {

        setRole(null);
        setUser(null);
        setIsAuthenticated(false);
        
        if (window.location.pathname !== '/login') {
          toast(t("loggedOutInAnotherTab") || "Logged out in another tab", {
            description: t("sessionEnded") || "Your session has ended"
          });
          
          navigate('/login?loggedOutInAnotherTab=true');
        }
      }
    };
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_OUT') {
        authChannel.postMessage('SIGNED_OUT');
      }
    });
    
    return () => {
      subscription.unsubscribe();
      authChannel.close();
    };
  }, [navigate, t]);

  return { 
    role, 
    loading,
    user,
    login,
    logout,
    handleSessionExpired,
    checkSession,
    isAdmin: role === 'admin',
    isAuthenticated,
    sessionChecked
  };
};
