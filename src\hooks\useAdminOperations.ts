import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "./useLanguage";

export interface CreateAdminData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  roleLevel?: number;
}

export interface CreateDistributorData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  parentDistributorId?: string;
  commissionPercentage: number;
  region?: string;
}

export const useAdminOperations = () => {
  const { t } = useLanguage();

  const createAdminAccount = async (adminData: CreateAdminData) => {
    try {
      // 1. Create user in auth.users using admin API
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: adminData.email,
        password: adminData.password,
        email_confirm: true
      });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      const userId = authData.user.id;

      // 2. Use the database function to create admin account
      const { data: adminId, error: adminError } = await supabase.rpc('create_admin_account', {
        auth_user_id: userId,
        admin_name: adminData.name,
        admin_email: adminData.email,
        admin_phone: adminData.phone || null,
        admin_role_level: adminData.roleLevel || 2
      });

      if (adminError) {
        // If admin creation fails, try to delete the auth user
        await supabase.auth.admin.deleteUser(userId);
        throw new Error(`Failed to create admin account: ${adminError.message}`);
      }

      toast(t("adminCreatedSuccess") || "تم إنشاء حساب المدير بنجاح", {
        description: t("adminCreatedDescription") || "تم إنشاء حساب المدير وإرسال بيانات الدخول"
      });

      return { success: true, userId, adminId };
    } catch (error) {
      console.error('Error creating admin account:', error);
      toast(t("adminCreationFailed") || "فشل في إنشاء حساب المدير", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      throw error;
    }
  };

  const createDistributorAccount = async (distributorData: CreateDistributorData) => {
    try {
      // 1. Create user in auth.users using admin API
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: distributorData.email,
        password: distributorData.password,
        email_confirm: true
      });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      const userId = authData.user.id;

      // 2. Use the database function to create distributor account
      const { data: distributorId, error: distributorError } = await supabase.rpc('create_distributor_account', {
        auth_user_id: userId,
        distributor_name: distributorData.name,
        distributor_email: distributorData.email,
        distributor_phone: distributorData.phone || null,
        parent_distributor_id: distributorData.parentDistributorId || null,
        commission_percentage: distributorData.commissionPercentage,
        distributor_region: distributorData.region || null
      });

      if (distributorError) {
        // If distributor creation fails, try to delete the auth user
        await supabase.auth.admin.deleteUser(userId);
        throw new Error(`Failed to create distributor account: ${distributorError.message}`);
      }

      toast(t("distributorCreatedSuccess") || "تم إنشاء حساب الموزع بنجاح", {
        description: t("distributorCreatedDescription") || "تم إنشاء حساب الموزع وإرسال بيانات الدخول"
      });

      return { success: true, userId, distributorId };
    } catch (error) {
      console.error('Error creating distributor account:', error);
      toast(t("distributorCreationFailed") || "فشل في إنشاء حساب الموزع", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      throw error;
    }
  };

  const getAllAdmins = async () => {
    try {
      const { data, error } = await supabase
        .from('admins')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching admins:', error);
      toast(t("fetchAdminsError") || "فشل في جلب بيانات المدراء", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return [];
    }
  };

  const getAllDistributors = async () => {
    try {
      const { data, error } = await supabase
        .from('distributors')
        .select(`
          *,
          parent_distributor:distributors!parent_distributor_id(name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching distributors:', error);
      toast(t("fetchDistributorsError") || "فشل في جلب بيانات الموزعين", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return [];
    }
  };

  const updateAdminStatus = async (adminId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('admins')
        .update({ is_active: isActive, updated_at: new Date().toISOString() })
        .eq('id', adminId);

      if (error) {
        throw error;
      }

      toast(t("adminStatusUpdated") || "تم تحديث حالة المدير", {
        description: isActive ? t("adminActivated") || "تم تفعيل المدير" : t("adminDeactivated") || "تم إلغاء تفعيل المدير"
      });

      return true;
    } catch (error) {
      console.error('Error updating admin status:', error);
      toast(t("updateAdminStatusError") || "فشل في تحديث حالة المدير", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  const updateDistributorStatus = async (distributorId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('distributors')
        .update({ is_active: isActive, updated_at: new Date().toISOString() })
        .eq('id', distributorId);

      if (error) {
        throw error;
      }

      toast(t("distributorStatusUpdated") || "تم تحديث حالة الموزع", {
        description: isActive ? t("distributorActivated") || "تم تفعيل الموزع" : t("distributorDeactivated") || "تم إلغاء تفعيل الموزع"
      });

      return true;
    } catch (error) {
      console.error('Error updating distributor status:', error);
      toast(t("updateDistributorStatusError") || "فشل في تحديث حالة الموزع", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  return {
    createAdminAccount,
    createDistributorAccount,
    getAllAdmins,
    getAllDistributors,
    updateAdminStatus,
    updateDistributorStatus
  };
};
