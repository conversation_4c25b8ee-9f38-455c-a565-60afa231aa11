
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )

    // Verify the user making the request is authenticated and is an admin
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    )

    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Check if the user is an admin
    const { data: userData, error: userDataError } = await supabaseClient
      .from('users')
      .select('email_type')
      .eq('id', user.id)
      .single()

    if (userDataError || userData?.email_type?.toLowerCase() !== 'admin') {
      throw new Error('User not authorized - admin access required')
    }

    // Get the request body
    const { email, password, name } = await req.json()

    if (!email || !password) {
      throw new Error('Email and password are required')
    }



    let userId = null;
    let userExistsInAuth = false;

    // Try to create the user using admin privileges
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: name || ''
      }
    })

    if (authError) {
      // Check if the error is because user already exists
      if (authError.message.includes('A user with this email address has already been registered')) {
        userExistsInAuth = true;
        
        // Use listUsers to find the user by email since getUserByEmail is not available
        const { data: existingUsers, error: listUsersError } = await supabaseAdmin.auth.admin.listUsers()
        
        if (listUsersError) {
          throw new Error(`Failed to list users: ${listUsersError.message}`)
        }
        
        // Find the user with the matching email
        const existingUser = existingUsers.users.find(u => u.email === email)
        
        if (!existingUser) {
          throw new Error('User not found in auth system despite existing email error')
        }
        
        userId = existingUser.id
        
        // Check if user exists in public.users table
        const { data: publicUserData, error: publicUserError } = await supabaseAdmin
          .from('users')
          .select('id')
          .eq('id', userId)
          .maybeSingle()
        
        if (publicUserError) {
          throw new Error(`Failed to check public users table: ${publicUserError.message}`)
        }
        
        if (publicUserData) {
          throw new Error('User already exists in both auth and public.users table')
        }
        

      } else {
        throw new Error(`Failed to create auth user: ${authError.message}`)
      }
    } else {
      if (!authData.user) {
        throw new Error('No user returned from auth creation')
      }
      userId = authData.user.id
    }

    return new Response(
      JSON.stringify({
        success: true,
        userId: userId,
        userExistsInAuth: userExistsInAuth,
        message: userExistsInAuth ? 'User exists in auth, ready for public.users data' : 'User created successfully'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
