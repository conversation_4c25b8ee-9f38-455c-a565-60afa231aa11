import { useEffect, useState, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useDistributorOperations, type DistributorStats } from "@/hooks/useDistributorOperations";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { 
  Users, 
  DollarSign, 
  TrendingUp, 
  UserCheck, 
  UserX, 
  Network,
  Activity,
  CreditCard,
  Clock,
  CheckCircle
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";

const DistributorDashboard = () => {
  const { t } = useLanguage();
  const { user, role, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const { getDistributorStats } = useDistributorOperations();
  const [stats, setStats] = useState<DistributorStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const distributorStats = await getDistributorStats();
      setStats(distributorStats);
    } catch (error) {
      console.error('Error fetching distributor stats:', error);
    } finally {
      setLoading(false);
    }
  }, [getDistributorStats]);

  useEffect(() => {
    if (user?.role === 'distributor' && user?.distributorId) {
      fetchStats();
    } else if (user && user.role !== 'distributor') {
      // If user is not a distributor, redirect them
      navigate('/dashboard');
    }
  }, [user, fetchStats, navigate]);

  // Show loading while auth is loading or while fetching stats
  if (authLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // If user is not a distributor, show error message
  if (user && user.role !== 'distributor') {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">
            {t("accessDenied") || "Access Denied"}
          </h1>
          <p className="text-muted-foreground mt-2">
            {t("distributorAccessOnly") || "This page is only accessible to distributors."}
          </p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("distributorDashboard") || "لوحة تحكم الموزع"}
          </h1>
          <p className="text-muted-foreground">
            {t("distributorDashboardDescription") || "إدارة المستخدمين والعمولات"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => navigate('/my-users')}>
            <Users className="mr-2 h-4 w-4" />
            {t("manageUsers") || "إدارة المستخدمين"}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Users */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalUsers") || "إجمالي المستخدمين"}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <UserCheck className="h-3 w-3 text-green-500" />
              <span>{stats?.activeUsers || 0} {t("active") || "نشط"}</span>
              <UserX className="h-3 w-3 text-red-500" />
              <span>{stats?.blockedUsers || 0} {t("blocked") || "محظور"}</span>
            </div>
          </CardContent>
        </Card>

        {/* Total Commissions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalCommissions") || "إجمالي العمولات"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats?.totalCommissions || 0)}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>{formatCurrency(stats?.paidCommissions || 0)} {t("paid") || "مدفوع"}</span>
            </div>
          </CardContent>
        </Card>

        {/* Pending Commissions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("pendingCommissions") || "العمولات المعلقة"}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats?.pendingCommissions || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t("awaitingPayment") || "في انتظار الدفع"}
            </p>
          </CardContent>
        </Card>

        {/* Sub-distributors */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("subDistributors") || "الموزعين الفرعيين"}
            </CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.subDistributors || 0}</div>
            <p className="text-xs text-muted-foreground">
              {user?.canCreateDistributors 
                ? t("canCreateMore") || "يمكن إنشاء المزيد"
                : t("cannotCreate") || "لا يمكن الإنشاء"
              }
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/my-users')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t("myUsers") || "مستخدميني"}
            </CardTitle>
            <CardDescription>
              {t("manageAssignedUsers") || "إدارة المستخدمين المعينين لك"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <Badge variant="secondary">{stats?.totalUsers || 0} {t("users") || "مستخدم"}</Badge>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/commissions')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t("commissions") || "العمولات"}
            </CardTitle>
            <CardDescription>
              {t("viewCommissionHistory") || "عرض تاريخ العمولات والأرباح"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <Badge variant="secondary">{stats?.totalOperations || 0} {t("operations") || "عملية"}</Badge>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        {user?.canCreateDistributors && (
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/sub-distributors')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                {t("subDistributors") || "الموزعين الفرعيين"}
              </CardTitle>
              <CardDescription>
                {t("manageSubDistributors") || "إدارة الموزعين الفرعيين"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge variant="secondary">{stats?.subDistributors || 0} {t("distributors") || "موزع"}</Badge>
                <Users className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Distributor Info */}
      <Card>
        <CardHeader>
          <CardTitle>{t("distributorInfo") || "معلومات الموزع"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("distributorLevel") || "مستوى الموزع"}
              </label>
              <p className="text-lg font-semibold">
                {t("level") || "المستوى"} {user?.distributorLevel || 1}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("commissionRate") || "معدل العمولة"}
              </label>
              <p className="text-lg font-semibold">
                {user?.commissionPercentage || 0}%
              </p>
            </div>
            {user?.region && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("region") || "المنطقة"}
                </label>
                <p className="text-lg font-semibold">{user.region}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DistributorDashboard;
