
import React, { createContext, useContext, useEffect, useState } from "react";
import { useFetchUsers } from "./useFetchUsers";
import { useFetchOperations } from "./useFetchOperations";
import { useDataActions } from "./useDataActions";
import { User, Operation } from "./types"; // Import User and Operation types
import { useAuth } from "../auth/AuthContext";
import { toast } from "@/components/ui/sonner";

// Define the SharedDataContextType here since it's not exported from types.ts
interface SharedDataContextType {
  users: User[];
  operations: Operation[];
  isLoading: boolean;
  isError: boolean;
  refreshData: () => Promise<void>;
  addCreditToUser: (userId: string, amount: number) => Promise<boolean>;
  refundOperation: (operation: Operation) => Promise<boolean>;
}

const DataContext = createContext<SharedDataContextType | undefined>(undefined);

export const DataProvider = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, user, isAdmin, role } = useAuth();
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Don't fetch data for distributors - they use their own hooks
  const shouldFetchData = isAuthenticated && role !== 'distributor';

  const {
    users,
    isLoading: isLoadingUsers,
    isError: isUsersError,
    refetch: refetchUsers
  } = useFetchUsers();

  const {
    operations,
    isLoading: isLoadingOperations,
    isError: isOperationsError,
    refetch: refetchOperations
  } = useFetchOperations();
  
  const { refreshData: originalRefreshData, addCreditToUser, refundOperation } = useDataActions();
  
  const refreshData = async () => {
    // Don't refresh data for distributors
    if (role === 'distributor') {
      return;
    }

    try {
      const userResults = await refetchUsers();
      const opsResults = await refetchOperations();

      // For regular users, check if we have their data
      if (!isAdmin && user && userResults.data) {
        if (userResults.data.length === 0 && retryCount < maxRetries) {

          setRetryCount(prev => prev + 1);
          setTimeout(() => refreshData(), 1000);
        } else if (userResults.data.length === 0) {

        } else {

          setRetryCount(0);
        }
      }

      originalRefreshData();
    } catch (error) {

      toast.error("Error", {
        description: "Failed to refresh data"
      });
    }
  };
  
  useEffect(() => {
    if (shouldFetchData && user) {

      refreshData();
    }
  }, [shouldFetchData, user]);
  
  useEffect(() => {
    if (shouldFetchData && user) {

      if (users.length > 0) {
        if (isAdmin) {
          // For admin, find their user data in the full list
          const adminUser = users.find(u => u.uid === user.id || u.id === user.id || u.UID === user.id);
          if (adminUser) {

          } else {

          }
        } else {
          // For regular user, the first item should be their data (since we're fetching only their data)

        }
      } else {

      }

      const userOps = operations.filter(op => op.UID === user.id);

      const refundedOps = userOps.filter(op => op.Status?.toLowerCase() === 'refunded');

    }
  }, [shouldFetchData, user, users, operations, isAdmin, retryCount]);
  
  const dataContext = {
    users,
    operations,
    isLoading: isLoadingUsers || isLoadingOperations,
    isError: isUsersError || isOperationsError,
    refreshData,
    addCreditToUser,
    refundOperation
  };
  
  return (
    <DataContext.Provider value={dataContext}>
      {children}
    </DataContext.Provider>
  );
};

export const useSharedData = (): SharedDataContextType => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error("useSharedData must be used within a DataProvider");
  }
  return context;
};

export { formatTimeString } from "./useDataActions";
export type { User, Operation } from "./types";
