import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { 
  Plus, 
  Search, 
  Users, 
  DollarSign, 
  MapPin, 
  Calendar,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  UserCheck,
  UserX
} from "lucide-react";
import { CreateDistributorDialog } from "@/components/distributors/CreateDistributorDialog";
import { EditDistributorDialog } from "@/components/distributors/EditDistributorDialog";
import { ViewDistributorDialog } from "@/components/distributors/ViewDistributorDialog";

interface Distributor {
  id: string;
  auth_user_id: string;
  name: string;
  email: string;
  phone?: string;
  parent_distributor_id?: string;
  distributor_level: number;
  commission_percentage: string;
  region?: string;
  max_sub_distributors: number;
  can_create_distributors: boolean;
  total_users: number;
  total_commission: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export default function DistributorsManager() {
  const { t, isRTL } = useLanguage();
  const { isAdmin } = useAuth();
  const [distributors, setDistributors] = useState<Distributor[]>([]);
  const [filteredDistributors, setFilteredDistributors] = useState<Distributor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDistributor, setSelectedDistributor] = useState<Distributor | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  // Fetch distributors
  const fetchDistributors = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('distributors')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setDistributors(data || []);
      setFilteredDistributors(data || []);
    } catch (error) {
      console.error('Error fetching distributors:', error);
      toast.error(t("errorFetchingData") || "خطأ في جلب البيانات");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAdmin) {
      fetchDistributors();
    }
  }, [isAdmin]);

  // Filter distributors based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredDistributors(distributors);
    } else {
      const filtered = distributors.filter(distributor =>
        distributor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        distributor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        distributor.region?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredDistributors(filtered);
    }
  }, [searchTerm, distributors]);

  const handleRefresh = () => {
    fetchDistributors();
    toast.success(t("dataRefreshed") || "تم تحديث البيانات");
  };

  const handleCreateDistributor = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditDistributor = (distributor: Distributor) => {
    setSelectedDistributor(distributor);
    setIsEditDialogOpen(true);
  };

  const handleViewDistributor = (distributor: Distributor) => {
    setSelectedDistributor(distributor);
    setIsViewDialogOpen(true);
  };

  const handleToggleStatus = async (distributor: Distributor) => {
    try {
      const { error } = await supabase
        .from('distributors')
        .update({ 
          is_active: !distributor.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', distributor.id);

      if (error) throw error;

      toast.success(
        distributor.is_active 
          ? t("distributorDeactivated") || "تم إلغاء تفعيل الموزع"
          : t("distributorActivated") || "تم تفعيل الموزع"
      );
      
      fetchDistributors();
    } catch (error) {
      console.error('Error toggling distributor status:', error);
      toast.error(t("errorUpdatingStatus") || "خطأ في تحديث الحالة");
    }
  };

  const handleDeleteDistributor = async (distributor: Distributor) => {
    if (!confirm(t("confirmDeleteDistributor") || "هل أنت متأكد من حذف هذا الموزع؟")) {
      return;
    }

    try {
      // First, check if distributor has users or sub-distributors
      const { data: users } = await supabase
        .from('users')
        .select('id')
        .eq('distributor_id', distributor.id);

      const { data: subDistributors } = await supabase
        .from('distributors')
        .select('id')
        .eq('parent_distributor_id', distributor.id);

      if (users && users.length > 0) {
        toast.error(t("cannotDeleteDistributorWithUsers") || "لا يمكن حذف موزع لديه مستخدمين");
        return;
      }

      if (subDistributors && subDistributors.length > 0) {
        toast.error(t("cannotDeleteDistributorWithSubs") || "لا يمكن حذف موزع لديه موزعين فرعيين");
        return;
      }

      // Delete the distributor
      const { error } = await supabase
        .from('distributors')
        .delete()
        .eq('id', distributor.id);

      if (error) throw error;

      // Delete from auth.users
      const { error: authError } = await supabase.auth.admin.deleteUser(distributor.auth_user_id);
      if (authError) {
        console.error('Error deleting auth user:', authError);
      }

      toast.success(t("distributorDeleted") || "تم حذف الموزع بنجاح");
      fetchDistributors();
    } catch (error) {
      console.error('Error deleting distributor:', error);
      toast.error(t("errorDeletingDistributor") || "خطأ في حذف الموزع");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  };

  const formatCurrency = (amount: string) => {
    return parseFloat(amount).toFixed(2);
  };

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">{t("accessDenied") || "غير مسموح بالوصول"}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("distributorsManager") || "إدارة الموزعين"}
          </h1>
          <p className="text-muted-foreground">
            {t("manageAllDistributors") || "إدارة جميع الموزعين في النظام"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            {t("refresh") || "تحديث"}
          </Button>
          <Button onClick={handleCreateDistributor}>
            <Plus className="mr-2 h-4 w-4" />
            {t("addDistributor") || "إضافة موزع"}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalDistributors") || "إجمالي الموزعين"}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{distributors.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("activeDistributors") || "الموزعين النشطين"}
            </CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {distributors.filter(d => d.is_active).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalUsers") || "إجمالي المستخدمين"}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {distributors.reduce((sum, d) => sum + d.total_users, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalCommissions") || "إجمالي العمولات"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                distributors.reduce((sum, d) => sum + parseFloat(d.total_commission), 0).toString()
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>{t("distributorsList") || "قائمة الموزعين"}</CardTitle>
          <CardDescription>
            {t("manageDistributorsDescription") || "إدارة وتعديل بيانات الموزعين"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("searchDistributors") || "البحث في الموزعين..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Distributors Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("name") || "الاسم"}</TableHead>
                  <TableHead>{t("email") || "البريد الإلكتروني"}</TableHead>
                  <TableHead>{t("level") || "المستوى"}</TableHead>
                  <TableHead>{t("commission") || "العمولة"}</TableHead>
                  <TableHead>{t("region") || "المنطقة"}</TableHead>
                  <TableHead>{t("users") || "المستخدمين"}</TableHead>
                  <TableHead>{t("status") || "الحالة"}</TableHead>
                  <TableHead>{t("joinDate") || "تاريخ الانضمام"}</TableHead>
                  <TableHead className={isRTL ? "text-right" : "text-left"}>
                    {t("actions") || "الإجراءات"}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      {t("loading") || "جاري التحميل..."}
                    </TableCell>
                  </TableRow>
                ) : filteredDistributors.length > 0 ? (
                  filteredDistributors.map((distributor) => (
                    <TableRow key={distributor.id}>
                      <TableCell className="font-medium">{distributor.name}</TableCell>
                      <TableCell>{distributor.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {t("level") || "المستوى"} {distributor.distributor_level}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono">
                        {distributor.commission_percentage}%
                      </TableCell>
                      <TableCell>
                        {distributor.region ? (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            {distributor.region}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{distributor.total_users}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={distributor.is_active ? "success" : "destructive"}>
                          {distributor.is_active 
                            ? t("active") || "نشط" 
                            : t("inactive") || "غير نشط"
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          {formatDate(distributor.created_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDistributor(distributor)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditDistributor(distributor)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(distributor)}
                          >
                            {distributor.is_active ? (
                              <UserX className="h-4 w-4" />
                            ) : (
                              <UserCheck className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDistributor(distributor)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      {t("noDistributorsFound") || "لا توجد موزعين"}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <CreateDistributorDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={() => {
          fetchDistributors();
          setIsCreateDialogOpen(false);
        }}
      />

      <EditDistributorDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        distributor={selectedDistributor}
        onSuccess={() => {
          fetchDistributors();
          setIsEditDialogOpen(false);
        }}
      />

      <ViewDistributorDialog
        isOpen={isViewDialogOpen}
        onClose={() => setIsViewDialogOpen(false)}
        distributor={selectedDistributor}
      />
    </div>
  );
}
