import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDistributorOperations, type DistributorUser } from "@/hooks/useDistributorOperations";
import { useLanguage } from "@/hooks/useLanguage";
import { AddUserDialog } from "@/components/distributor/AddUserDialog";
import { 
  Users, 
  Search, 
  MoreHorizontal, 
  UserCheck, 
  UserX, 
  Shield, 
  ShieldOff,
  Eye,
  Edit,
  Plus
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/sonner";

const MyUsers = () => {
  const { t } = useLanguage();
  const { getMyUsers, updateUserStatus, blockUser } = useDistributorOperations();
  const [users, setUsers] = useState<DistributorUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<DistributorUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const distributorUsers = await getMyUsers();
        setUsers(distributorUsers);
        setFilteredUsers(distributorUsers);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [getMyUsers]);

  useEffect(() => {
    const filtered = users.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [searchTerm, users]);

  const handleStatusChange = async (userId: string, newStatus: 'Active' | 'Inactive') => {
    const success = await updateUserStatus(userId, newStatus);
    if (success) {
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, activate: newStatus } : user
      ));
    }
  };

  const handleBlockChange = async (userId: string, newBlockStatus: 'Blocked' | 'Not Blocked') => {
    const success = await blockUser(userId, newBlockStatus);
    if (success) {
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, block: newBlockStatus } : user
      ));
    }
  };

  const getStatusBadge = (activate: string) => {
    return activate === 'Active' ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <UserCheck className="w-3 h-3 mr-1" />
        {t("active") || "نشط"}
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-gray-100 text-gray-800">
        <UserX className="w-3 h-3 mr-1" />
        {t("inactive") || "غير نشط"}
      </Badge>
    );
  };

  const getBlockBadge = (block: string) => {
    return block === 'Blocked' ? (
      <Badge variant="destructive">
        <Shield className="w-3 h-3 mr-1" />
        {t("blocked") || "محظور"}
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
        <ShieldOff className="w-3 h-3 mr-1" />
        {t("notBlocked") || "غير محظور"}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("myUsers") || "مستخدميني"}
          </h1>
          <p className="text-muted-foreground">
            {t("manageAssignedUsers") || "إدارة المستخدمين المعينين لك"}
          </p>
        </div>
        <Button onClick={() => setIsAddUserDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t("addUser") || "إضافة مستخدم"}
        </Button>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t("usersList") || "قائمة المستخدمين"}
          </CardTitle>
          <CardDescription>
            {t("totalUsers") || "إجمالي المستخدمين"}: {users.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t("searchUsers") || "البحث في المستخدمين..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("name") || "الاسم"}</TableHead>
                  <TableHead>{t("email") || "البريد الإلكتروني"}</TableHead>
                  <TableHead>{t("userType") || "نوع المستخدم"}</TableHead>
                  <TableHead>{t("credits") || "الرصيد"}</TableHead>
                  <TableHead>{t("status") || "الحالة"}</TableHead>
                  <TableHead>{t("blockStatus") || "حالة الحظر"}</TableHead>
                  <TableHead>{t("joinDate") || "تاريخ الانضمام"}</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {searchTerm 
                            ? t("noUsersFound") || "لم يتم العثور على مستخدمين"
                            : t("noUsersYet") || "لا يوجد مستخدمين بعد"
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{user.user_type}</Badge>
                      </TableCell>
                      <TableCell className="font-mono">{user.credits}</TableCell>
                      <TableCell>{getStatusBadge(user.activate)}</TableCell>
                      <TableCell>{getBlockBadge(user.block)}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {formatDate(user.created_at)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>{t("actions") || "الإجراءات"}</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              {t("viewDetails") || "عرض التفاصيل"}
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              {t("editUser") || "تعديل المستخدم"}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(
                                user.id, 
                                user.activate === 'Active' ? 'Inactive' : 'Active'
                              )}
                            >
                              {user.activate === 'Active' ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  {t("deactivate") || "إلغاء التفعيل"}
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  {t("activate") || "تفعيل"}
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleBlockChange(
                                user.id,
                                user.block === 'Blocked' ? 'Not Blocked' : 'Blocked'
                              )}
                              className={user.block === 'Blocked' ? "text-green-600" : "text-red-600"}
                            >
                              {user.block === 'Blocked' ? (
                                <>
                                  <ShieldOff className="mr-2 h-4 w-4" />
                                  {t("unblock") || "إلغاء الحظر"}
                                </>
                              ) : (
                                <>
                                  <Shield className="mr-2 h-4 w-4" />
                                  {t("block") || "حظر"}
                                </>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add User Dialog */}
      <AddUserDialog
        isOpen={isAddUserDialogOpen}
        onClose={() => setIsAddUserDialogOpen(false)}
        onSuccess={() => {
          setIsAddUserDialogOpen(false);
          // Refresh users list
          const fetchUsers = async () => {
            try {
              setLoading(true);
              const distributorUsers = await getMyUsers();
              setUsers(distributorUsers);
              setFilteredUsers(distributorUsers);
            } catch (error) {
              console.error('Error fetching users:', error);
            } finally {
              setLoading(false);
            }
          };
          fetchUsers();
        }}
      />
    </div>
  );
};

export default MyUsers;
