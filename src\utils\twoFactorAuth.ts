// Custom 2FA implementation using native browser APIs and qrcode-generator
import qrcode from 'qrcode-generator';

// Base32 encoding/decoding functions
const BASE32_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

function base32Encode(buffer: Uint8Array): string {
  let result = '';
  let bits = 0;
  let value = 0;
  
  for (let i = 0; i < buffer.length; i++) {
    value = (value << 8) | buffer[i];
    bits += 8;
    
    while (bits >= 5) {
      result += BASE32_CHARS[(value >>> (bits - 5)) & 31];
      bits -= 5;
    }
  }
  
  if (bits > 0) {
    result += BASE32_CHARS[(value << (5 - bits)) & 31];
  }
  
  // Add padding
  while (result.length % 8 !== 0) {
    result += '=';
  }
  
  return result;
}

function base32Decode(encoded: string): Uint8Array {
  encoded = encoded.toUpperCase().replace(/=+$/, '');
  const buffer = new Uint8Array(Math.floor(encoded.length * 5 / 8));
  let bits = 0;
  let value = 0;
  let index = 0;
  
  for (let i = 0; i < encoded.length; i++) {
    const char = encoded[i];
    const charIndex = BASE32_CHARS.indexOf(char);
    if (charIndex === -1) continue;
    
    value = (value << 5) | charIndex;
    bits += 5;
    
    if (bits >= 8) {
      buffer[index++] = (value >>> (bits - 8)) & 255;
      bits -= 8;
    }
  }
  
  return buffer.slice(0, index);
}

// HMAC-SHA1 implementation using Web Crypto API
async function hmacSha1(key: Uint8Array, message: Uint8Array): Promise<Uint8Array> {
  try {
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      key,
      { name: 'HMAC', hash: 'SHA-1' },
      false,
      ['sign']
    );
    
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, message);
    return new Uint8Array(signature);
  } catch (error) {
    // Fallback for browsers that don't support Web Crypto API
    throw new Error('Browser does not support required cryptographic functions');
  }
}

// Generate a random secret
export function generateSecret(): string {
  try {
    // Use crypto.getRandomValues for better browser compatibility
    const array = new Uint8Array(20); // 160 bits
    crypto.getRandomValues(array);
    return base32Encode(array);
  } catch (error) {
    // Fallback using Math.random (less secure but more compatible)
    console.warn('Using fallback random generation - less secure');
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}



// Verify TOTP token
export async function verifyTOTP(token: string, secret: string, window: number = 1): Promise<boolean> {
  try {
    const timeStep = 30;
    const currentTime = Math.floor(Date.now() / 1000 / timeStep);
    
    // Check current time and adjacent time windows
    for (let i = -window; i <= window; i++) {
      const testTime = currentTime + i;
      const testToken = await generateTOTPForTime(secret, testTime);
      if (testToken === token) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

// Generate TOTP for specific time
async function generateTOTPForTime(secret: string, time: number): Promise<string> {
  try {
    const secretBytes = base32Decode(secret);
    
    // Convert time to 8-byte array
    const timeBytes = new Uint8Array(8);
    let timeValue = time;
    for (let i = 7; i >= 0; i--) {
      timeBytes[i] = timeValue & 0xff;
      timeValue = Math.floor(timeValue / 256);
    }
    
    const hmac = await hmacSha1(secretBytes, timeBytes);
    
    // Dynamic truncation
    const offset = hmac[hmac.length - 1] & 0xf;
    const code = ((hmac[offset] & 0x7f) << 24) |
                 ((hmac[offset + 1] & 0xff) << 16) |
                 ((hmac[offset + 2] & 0xff) << 8) |
                 (hmac[offset + 3] & 0xff);
    
    const otp = (code % 1000000).toString();
    return otp.padStart(6, '0');
  } catch (error) {
    throw error;
  }
}

// Generate OTP Auth URL
export function generateOTPAuthURL(email: string, issuer: string, secret: string): string {
  const params = new URLSearchParams({
    secret: secret,
    issuer: issuer,
    algorithm: 'SHA1',
    digits: '6',
    period: '30'
  });
  
  return `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(email)}?${params.toString()}`;
}



// Generate QR Code using qrcode-generator library
export function generateQRCodeSVG(text: string, size: number = 256): string {
  try {
    // Create QR code instance with optimal settings
    const qr = qrcode(0, 'M'); // Type 0 (auto-detect), Error correction level M (15%)
    qr.addData(text);
    qr.make();

    // Get the module count
    const moduleCount = qr.getModuleCount();
    const cellSize = Math.floor(size / (moduleCount + 2)); // Add padding
    const actualSize = cellSize * (moduleCount + 2);
    const offset = cellSize; // 1 cell padding

    // Generate SVG with proper structure
    let svg = `<svg width="${actualSize}" height="${actualSize}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${actualSize} ${actualSize}">`;

    // White background
    svg += `<rect width="${actualSize}" height="${actualSize}" fill="white"/>`;

    // Draw QR code modules
    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (qr.isDark(row, col)) {
          const x = offset + col * cellSize;
          const y = offset + row * cellSize;
          svg += `<rect x="${x}" y="${y}" width="${cellSize}" height="${cellSize}" fill="black"/>`;
        }
      }
    }

    svg += '</svg>';
    return svg;
  } catch (error) {
    return generatePlaceholderQR(size);
  }
}

// Fallback placeholder QR code
function generatePlaceholderQR(size: number): string {
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${size} ${size}">
    <rect width="${size}" height="${size}" fill="white" stroke="#ccc" stroke-width="2"/>
    <rect x="10" y="10" width="30" height="30" fill="black"/>
    <rect x="${size-40}" y="10" width="30" height="30" fill="black"/>
    <rect x="10" y="${size-40}" width="30" height="30" fill="black"/>
    <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
      QR Code
    </text>
    <text x="${size/2}" y="${size/2 + 25}" text-anchor="middle" dominant-baseline="middle" font-family="Arial, sans-serif" font-size="12" fill="#999">
      يرجى المحاولة مرة أخرى
    </text>
  </svg>`;
}


// Generate QR Code as Canvas Data URL (alternative method)
export function generateQRCodeCanvas(text: string, size: number = 256): string {
  try {
    const qr = qrcode(0, 'M');
    qr.addData(text);
    qr.make();

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Canvas context not available');

    const moduleCount = qr.getModuleCount();
    const cellSize = Math.floor(size / (moduleCount + 2));
    const actualSize = cellSize * (moduleCount + 2);
    const offset = cellSize;

    canvas.width = actualSize;
    canvas.height = actualSize;

    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, actualSize, actualSize);

    ctx.fillStyle = 'black';
    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (qr.isDark(row, col)) {
          const x = offset + col * cellSize;
          const y = offset + row * cellSize;
          ctx.fillRect(x, y, cellSize, cellSize);
        }
      }
    }

    return canvas.toDataURL('image/png');
  } catch (error) {
    return 'data:image/svg+xml;base64,' + btoa(`<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg"><rect width="${size}" height="${size}" fill="white"/><text x="${size/2}" y="${size/2}" text-anchor="middle" fill="black">QR Error</text></svg>`);
  }
}

// Convert SVG to Data URL
export function svgToDataURL(svg: string): string {
  const base64 = btoa(unescape(encodeURIComponent(svg)));
  return `data:image/svg+xml;base64,${base64}`;
}
