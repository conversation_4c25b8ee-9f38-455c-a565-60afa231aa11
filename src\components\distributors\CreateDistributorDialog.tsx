import { useState } from "react";
import { Di<PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from "@/hooks/useLanguage";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { Loader2, User, Mail, Phone, MapPin, Percent, Users, Shield } from "lucide-react";

interface CreateDistributorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  email: string;
  password: string;
  phone: string;
  region: string;
  commissionPercentage: number;
  maxSubDistributors: number;
  canCreateDistributors: boolean;
  distributorLevel: number;
}

export function CreateDistributorDialog({ isOpen, onClose, onSuccess }: CreateDistributorDialogProps) {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    phone: "",
    region: "",
    commissionPercentage: 10,
    maxSubDistributors: 5,
    canCreateDistributors: true,
    distributorLevel: 1
  });

  const handleInputChange = (field: keyof FormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast.error(t("nameRequired") || "الاسم مطلوب");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error(t("emailRequired") || "البريد الإلكتروني مطلوب");
      return false;
    }
    if (!formData.password || formData.password.length < 8) {
      toast.error(t("passwordMinLength") || "كلمة المرور يجب أن تكون 8 أحرف على الأقل");
      return false;
    }
    if (formData.commissionPercentage < 0 || formData.commissionPercentage > 100) {
      toast.error(t("invalidCommissionPercentage") || "نسبة العمولة غير صحيحة");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      // 1. Create user in auth.users
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: formData.email,
        password: formData.password,
        email_confirm: true,
        user_metadata: {
          name: formData.name
        }
      });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      const userId = authData.user.id;

      // 2. Create distributor record
      const { error: distributorError } = await supabase
        .from('distributors')
        .insert({
          auth_user_id: userId,
          name: formData.name,
          email: formData.email,
          phone: formData.phone || null,
          parent_distributor_id: null, // Top-level distributor created by admin
          distributor_level: formData.distributorLevel,
          commission_percentage: formData.commissionPercentage.toString(),
          region: formData.region || null,
          max_sub_distributors: formData.maxSubDistributors,
          can_create_distributors: formData.canCreateDistributors,
          total_users: 0,
          total_commission: "0.00",
          is_active: true
        });

      if (distributorError) {
        // If distributor creation fails, try to delete the auth user
        await supabase.auth.admin.deleteUser(userId);
        throw new Error(`Failed to create distributor: ${distributorError.message}`);
      }

      // 3. Add to account_types table
      const { error: accountTypeError } = await supabase
        .from('account_types')
        .insert({
          auth_user_id: userId,
          account_type: 'distributor'
        });

      if (accountTypeError) {
        console.error('Error adding to account_types:', accountTypeError);
        // Don't fail the whole operation for this
      }

      toast.success(t("distributorCreatedSuccess") || "تم إنشاء الموزع بنجاح");
      onSuccess();
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        password: "",
        phone: "",
        region: "",
        commissionPercentage: 10,
        maxSubDistributors: 5,
        canCreateDistributors: true,
        distributorLevel: 1
      });

    } catch (error) {
      console.error('Error creating distributor:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : t("distributorCreationFailed") || "فشل في إنشاء الموزع"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("createNewDistributor") || "إنشاء موزع جديد"}</DialogTitle>
          <DialogDescription>
            {t("createDistributorDescription") || "إنشاء حساب موزع جديد في النظام"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("basicInformation") || "المعلومات الأساسية"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  <User className="inline mr-1 h-4 w-4" />
                  {t("fullName") || "الاسم الكامل"} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t("enterFullName") || "أدخل الاسم الكامل"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  <Mail className="inline mr-1 h-4 w-4" />
                  {t("email") || "البريد الإلكتروني"} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"}
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">
                  <Shield className="inline mr-1 h-4 w-4" />
                  {t("password") || "كلمة المرور"} *
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder={t("enterPassword") || "أدخل كلمة المرور"}
                  minLength={8}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  <Phone className="inline mr-1 h-4 w-4" />
                  {t("phone") || "رقم الهاتف"}
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t("enterPhone") || "أدخل رقم الهاتف"}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="region">
                <MapPin className="inline mr-1 h-4 w-4" />
                {t("region") || "المنطقة"}
              </Label>
              <Input
                id="region"
                value={formData.region}
                onChange={(e) => handleInputChange('region', e.target.value)}
                placeholder={t("enterRegion") || "أدخل المنطقة"}
              />
            </div>
          </div>

          {/* Distributor Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("distributorSettings") || "إعدادات الموزع"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="commissionPercentage">
                  <Percent className="inline mr-1 h-4 w-4" />
                  {t("commissionPercentage") || "نسبة العمولة"} (%)
                </Label>
                <Input
                  id="commissionPercentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.commissionPercentage}
                  onChange={(e) => handleInputChange('commissionPercentage', parseFloat(e.target.value) || 0)}
                  placeholder="10.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="distributorLevel">
                  {t("distributorLevel") || "مستوى الموزع"}
                </Label>
                <Input
                  id="distributorLevel"
                  type="number"
                  min="1"
                  max="5"
                  value={formData.distributorLevel}
                  onChange={(e) => handleInputChange('distributorLevel', parseInt(e.target.value) || 1)}
                  placeholder="1"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="maxSubDistributors">
                  <Users className="inline mr-1 h-4 w-4" />
                  {t("maxSubDistributors") || "الحد الأقصى للموزعين الفرعيين"}
                </Label>
                <Input
                  id="maxSubDistributors"
                  type="number"
                  min="0"
                  max="50"
                  value={formData.maxSubDistributors}
                  onChange={(e) => handleInputChange('maxSubDistributors', parseInt(e.target.value) || 0)}
                  placeholder="5"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="canCreateDistributors">
                  {t("canCreateSubDistributors") || "يمكنه إنشاء موزعين فرعيين"}
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="canCreateDistributors"
                    checked={formData.canCreateDistributors}
                    onCheckedChange={(checked) => handleInputChange('canCreateDistributors', checked)}
                  />
                  <Label htmlFor="canCreateDistributors" className="text-sm text-muted-foreground">
                    {formData.canCreateDistributors 
                      ? t("enabled") || "مفعل" 
                      : t("disabled") || "معطل"
                    }
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("cancel") || "إلغاء"}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting 
                ? t("creating") || "جاري الإنشاء..." 
                : t("createDistributor") || "إنشاء الموزع"
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
