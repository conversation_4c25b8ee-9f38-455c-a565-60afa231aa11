// Test utility to verify distributor login functionality
import { supabase } from "@/integrations/supabase/client";

export const testDistributorLogin = async () => {
  try {
    console.log('Testing distributor login...');
    
    // Attempt to sign in with distributor credentials
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'TestDistributor123!'
    });

    if (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: error.message
      };
    }

    if (!data.user) {
      console.error('No user data returned');
      return {
        success: false,
        error: 'No user data returned'
      };
    }

    console.log('Login successful:', data.user.email);
    
    // Test the get_account_info function
    const { data: accountInfo, error: accountError } = await supabase.rpc('get_account_info', {
      user_id: data.user.id
    });

    if (accountError) {
      console.error('Account info error:', accountError);
      return {
        success: false,
        error: `Account info failed: ${accountError.message}`
      };
    }

    console.log('Account info:', accountInfo);

    // Sign out after test
    await supabase.auth.signOut();

    return {
      success: true,
      user: data.user,
      accountInfo: accountInfo
    };

  } catch (error) {
    console.error('Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Function to test from browser console
(window as any).testDistributorLogin = testDistributorLogin;
