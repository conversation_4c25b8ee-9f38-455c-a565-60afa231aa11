
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export function useServerData() {
  return useQuery({
    queryKey: ['certsave'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('certsave')
        .select('*')
        .order('Imei');

      if (error) {
        throw error;
      }

      return data;
    }
  });
}
