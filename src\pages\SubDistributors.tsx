import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useDistributorOperations, type SubDistributor } from "@/hooks/useDistributorOperations";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { AddSubDistributorDialog } from "@/components/distributor/AddSubDistributorDialog";
import { 
  Network, 
  Search, 
  Plus, 
  Users, 
  DollarSign, 
  MapPin,
  Calendar,
  TrendingUp,
  Building
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";


const SubDistributors = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { getSubDistributors } = useDistributorOperations();
  const [subDistributors, setSubDistributors] = useState<SubDistributor[]>([]);
  const [filteredSubDistributors, setFilteredSubDistributors] = useState<SubDistributor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  useEffect(() => {
    const fetchSubDistributors = async () => {
      try {
        setLoading(true);
        const distributors = await getSubDistributors();
        setSubDistributors(distributors);
        setFilteredSubDistributors(distributors);
      } catch (error) {
        console.error('Error fetching sub-distributors:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user?.canCreateDistributors) {
      fetchSubDistributors();
    } else {
      setLoading(false);
    }
  }, [getSubDistributors, user?.canCreateDistributors]);

  useEffect(() => {
    const filtered = subDistributors.filter(distributor =>
      distributor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      distributor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (distributor.region || '').toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredSubDistributors(filtered);
  }, [searchTerm, subDistributors]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        {t("active") || "نشط"}
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-gray-100 text-gray-800">
        {t("inactive") || "غير نشط"}
      </Badge>
    );
  };

  const handleSubDistributorCreated = (newSubDistributor: SubDistributor) => {
    setSubDistributors(prev => [newSubDistributor, ...prev]);
    setIsCreateDialogOpen(false);
  };

  if (!user?.canCreateDistributors) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("subDistributors") || "الموزعين الفرعيين"}
          </h1>
          <p className="text-muted-foreground">
            {t("manageSubDistributors") || "إدارة الموزعين الفرعيين"}
          </p>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Network className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t("noPermissionToCreateDistributors") || "لا توجد صلاحية لإنشاء موزعين"}
            </h3>
            <p className="text-muted-foreground text-center">
              {t("contactAdminForPermission") || "تواصل مع المدير للحصول على صلاحية إنشاء موزعين فرعيين"}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("subDistributors") || "الموزعين الفرعيين"}
          </h1>
          <p className="text-muted-foreground">
            {t("manageSubDistributors") || "إدارة الموزعين الفرعيين"}
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t("createSubDistributor") || "إنشاء موزع فرعي"}
        </Button>
      </div>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalSubDistributors") || "إجمالي الموزعين الفرعيين"}
            </CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subDistributors.length}</div>
            <p className="text-xs text-muted-foreground">
              {subDistributors.filter(d => d.is_active).length} {t("active") || "نشط"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalUsers") || "إجمالي المستخدمين"}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {subDistributors.reduce((sum, d) => sum + d.total_users, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t("acrossAllSubDistributors") || "عبر جميع الموزعين الفرعيين"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalCommissions") || "إجمالي العمولات"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(subDistributors.reduce((sum, d) => sum + d.total_commission, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              {t("fromSubDistributors") || "من الموزعين الفرعيين"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Sub-Distributors Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            {t("subDistributorsList") || "قائمة الموزعين الفرعيين"}
          </CardTitle>
          <CardDescription>
            {t("manageYourSubDistributors") || "إدارة الموزعين الفرعيين التابعين لك"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t("searchSubDistributors") || "البحث في الموزعين الفرعيين..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("name") || "الاسم"}</TableHead>
                  <TableHead>{t("email") || "البريد الإلكتروني"}</TableHead>
                  <TableHead>{t("level") || "المستوى"}</TableHead>
                  <TableHead>{t("commissionRate") || "معدل العمولة"}</TableHead>
                  <TableHead>{t("region") || "المنطقة"}</TableHead>
                  <TableHead>{t("users") || "المستخدمين"}</TableHead>
                  <TableHead>{t("commissions") || "العمولات"}</TableHead>
                  <TableHead>{t("status") || "الحالة"}</TableHead>
                  <TableHead>{t("joinDate") || "تاريخ الانضمام"}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubDistributors.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Network className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {searchTerm 
                            ? t("noSubDistributorsFound") || "لم يتم العثور على موزعين فرعيين"
                            : t("noSubDistributorsYet") || "لا يوجد موزعين فرعيين بعد"
                          }
                        </p>
                        {!searchTerm && (
                          <Button 
                            variant="outline" 
                            onClick={() => setIsCreateDialogOpen(true)}
                            className="mt-2"
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            {t("createFirstSubDistributor") || "إنشاء أول موزع فرعي"}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSubDistributors.map((distributor) => (
                    <TableRow key={distributor.id}>
                      <TableCell className="font-medium">{distributor.name}</TableCell>
                      <TableCell>{distributor.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {t("level") || "المستوى"} {distributor.distributor_level}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono">{distributor.commission_percentage}%</TableCell>
                      <TableCell>
                        {distributor.region ? (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            {distributor.region}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3 text-muted-foreground" />
                          {distributor.total_users}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono">
                        {formatCurrency(distributor.total_commission)}
                      </TableCell>
                      <TableCell>{getStatusBadge(distributor.is_active)}</TableCell>
                      <TableCell className="text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(distributor.created_at)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Sub-Distributor Dialog */}
      <AddSubDistributorDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={() => {
          setIsCreateDialogOpen(false);
          // Refresh sub-distributors list
          const fetchSubDistributors = async () => {
            try {
              setLoading(true);
              const distributors = await getSubDistributors();
              setSubDistributors(distributors);
              setFilteredSubDistributors(distributors);
            } catch (error) {
              console.error('Error fetching sub-distributors:', error);
            } finally {
              setLoading(false);
            }
          };
          fetchSubDistributors();
        }}
      />
    </div>
  );
};

export default SubDistributors;
