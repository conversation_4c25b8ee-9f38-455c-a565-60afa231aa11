import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDistributorOperations, type SubDistributor } from "@/hooks/useDistributorOperations";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { Loader2, User, Mail, Phone, MapPin, Percent } from "lucide-react";
import { toast } from "@/components/ui/sonner";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  phone: z.string().optional(),
  commissionPercentage: z.number().min(0).max(100),
  region: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface CreateSubDistributorFormProps {
  onSuccess: (subDistributor: SubDistributor) => void;
}

const CreateSubDistributorForm = ({ onSuccess }: CreateSubDistributorFormProps) => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { createSubDistributor } = useDistributorOperations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      phone: "",
      commissionPercentage: Math.max(0, (user?.commissionPercentage || 10) - 2), // Default to 2% less than parent
      region: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);

      const result = await createSubDistributor({
        email: data.email,
        password: data.password,
        name: data.name,
        phone: data.phone,
        commissionPercentage: data.commissionPercentage,
        region: data.region,
      });

      if (result.success) {
        // Create a SubDistributor object for the callback
        const newSubDistributor: SubDistributor = {
          id: result.distributorId,
          auth_user_id: result.userId,
          name: data.name,
          email: data.email,
          phone: data.phone,
          distributor_level: (user?.distributorLevel || 1) + 1,
          commission_percentage: data.commissionPercentage,
          region: data.region,
          total_users: 0,
          total_commission: 0,
          is_active: true,
          created_at: new Date().toISOString(),
        };

        onSuccess(newSubDistributor);
        form.reset();
        
        toast(t("subDistributorCreatedSuccess") || "تم إنشاء الموزع الفرعي بنجاح", {
          description: t("subDistributorCreatedDescription") || "تم إنشاء الموزع الفرعي وإرسال بيانات الدخول"
        });
      }
    } catch (error) {
      console.error('Error creating sub-distributor:', error);
      toast(t("subDistributorCreationFailed") || "فشل في إنشاء الموزع الفرعي", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const maxCommissionPercentage = Math.max(0, (user?.commissionPercentage || 10) - 1);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">{t("basicInformation") || "المعلومات الأساسية"}</h3>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("fullName") || "الاسم الكامل"}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("enterFullName") || "أدخل الاسم الكامل"} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("email") || "البريد الإلكتروني"}</FormLabel>
                  <FormControl>
                    <Input 
                      type="email" 
                      placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"} 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("password") || "كلمة المرور"}</FormLabel>
                  <FormControl>
                    <Input 
                      type="password" 
                      placeholder={t("enterPassword") || "أدخل كلمة المرور"} 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    {t("passwordRequirement") || "يجب أن تكون كلمة المرور 8 أحرف على الأقل"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("phone") || "رقم الهاتف"} ({t("optional") || "اختياري"})</FormLabel>
                  <FormControl>
                    <Input placeholder={t("enterPhone") || "أدخل رقم الهاتف"} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Business Information */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Percent className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">{t("businessInformation") || "المعلومات التجارية"}</h3>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="commissionPercentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("commissionPercentage") || "نسبة العمولة"}</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      min="0" 
                      max={maxCommissionPercentage}
                      step="0.1"
                      placeholder="0.0"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("maxCommissionRate") || "الحد الأقصى"}: {maxCommissionPercentage}% 
                    ({t("yourRate") || "معدلك"}: {user?.commissionPercentage || 0}%)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="region"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("region") || "المنطقة"} ({t("optional") || "اختياري"})</FormLabel>
                  <FormControl>
                    <Input placeholder={t("enterRegion") || "أدخل المنطقة"} {...field} />
                  </FormControl>
                  <FormDescription>
                    {t("regionDescription") || "المنطقة الجغرافية التي سيعمل بها الموزع"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Hierarchy Information */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">{t("hierarchyInformation") || "معلومات التسلسل الهرمي"}</h4>
          <div className="grid gap-2 text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>{t("parentDistributor") || "الموزع الأب"}:</span>
              <span className="font-medium">{user?.name}</span>
            </div>
            <div className="flex justify-between">
              <span>{t("newDistributorLevel") || "مستوى الموزع الجديد"}:</span>
              <span className="font-medium">{(user?.distributorLevel || 1) + 1}</span>
            </div>
            <div className="flex justify-between">
              <span>{t("yourCommissionRate") || "معدل عمولتك"}:</span>
              <span className="font-medium">{user?.commissionPercentage || 0}%</span>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-2">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("creating") || "جاري الإنشاء..."}
              </>
            ) : (
              <>
                <User className="mr-2 h-4 w-4" />
                {t("createSubDistributor") || "إنشاء موزع فرعي"}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default CreateSubDistributorForm;
