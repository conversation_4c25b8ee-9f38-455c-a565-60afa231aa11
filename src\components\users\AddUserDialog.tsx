
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON>alogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "@/hooks/useLanguage";
import { usePricingPlans } from "@/hooks/usePricingPlans";

interface AddUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (newUser: any) => Promise<boolean> | boolean;
}

// Helper function to generate random name
const generateRandomName = () => {
  const randomNumber = Math.floor(Math.random() * 9999) + 1;
  return `Pegasus User ${randomNumber}`;
};

// Helper function to generate random phone number
const generateRandomPhone = () => {
  let phone = "";
  for (let i = 0; i < 9; i++) {
    phone += Math.floor(Math.random() * 10);
  }
  return phone;
};

export function AddUserDialog({
  isOpen,
  onClose,
  onSave
}: AddUserDialogProps) {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [credits, setCredits] = useState("0");
  const [userType, setUserType] = useState("Credits License");
  const [phone, setPhone] = useState("");
  const [country, setCountry] = useState("Saudi Arabia");
  const [subscriptionMonths, setSubscriptionMonths] = useState("3");
  const [selectedPlan, setSelectedPlan] = useState("free");
  const [showSubscriptionMonths, setShowSubscriptionMonths] = useState(false);
  const [loading, setLoading] = useState(false);
  const { t, isRTL } = useLanguage();
  const { data: pricingPlans = [], isLoading: plansLoading } = usePricingPlans();

  const countries = [
    "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", 
    "Argentina", "Armenia", "Australia", "Austria", "Azerbaijan", "Bahamas", 
    "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", 
    "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", 
    "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia", "Cameroon", 
    "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", 
    "Comoros", "Congo", "Costa Rica", "Croatia", "Cuba", "Cyprus", "Czech Republic", 
    "Democratic Republic of the Congo", "Denmark", "Djibouti", "Dominica", 
    "Dominican Republic", "Ecuador", "Egypt", "El Salvador", "Equatorial Guinea", 
    "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France", 
    "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", 
    "Guatemala", "Guinea", "Guinea-Bissau", "Guyana", "Haiti", "Honduras", 
    "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", 
    "Israel", "Italy", "Ivory Coast", "Jamaica", "Japan", "Jordan", "Kazakhstan", 
    "Kenya", "Kiribati", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", 
    "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", 
    "Luxembourg", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", 
    "Marshall Islands", "Mauritania", "Mauritius", "Mexico", "Micronesia", 
    "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", 
    "Myanmar", "Namibia", "Nauru", "Nepal", "Netherlands", "New Zealand", 
    "Nicaragua", "Niger", "Nigeria", "North Korea", "North Macedonia", "Norway", 
    "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", 
    "Paraguay", "Peru", "Philippines", "Poland", "Portugal", "Qatar", "Romania", 
    "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", 
    "Saint Vincent and the Grenadines", "Samoa", "San Marino", 
    "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", 
    "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", 
    "Somalia", "South Africa", "South Korea", "South Sudan", "Spain", "Sri Lanka", 
    "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan", 
    "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", 
    "Tunisia", "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine", 
    "United Arab Emirates", "United Kingdom", "United States", "Uruguay", 
    "Uzbekistan", "Vanuatu", "Vatican City", "Venezuela", "Vietnam", "Yemen", 
    "Zambia", "Zimbabwe"
  ];

  // Generate random data when dialog opens
  useEffect(() => {
    if (isOpen) {
      setName(generateRandomName());
      setPhone(generateRandomPhone());
    }
  }, [isOpen]);

  useEffect(() => {
    setShowSubscriptionMonths(userType === "Monthly License");
  }, [userType]);

  // Update user type when plan changes
  useEffect(() => {
    if (selectedPlan === "free") {
      setUserType("Credits License");
    } else {
      const plan = pricingPlans.find(p => p.id === selectedPlan);
      if (plan && plan.duration_months && parseInt(plan.duration_months) > 0) {
        setUserType("Monthly License");
        setSubscriptionMonths(plan.duration_months);
      }
    }
  }, [selectedPlan, pricingPlans]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Get current date
    const startDate = new Date().toISOString().split('T')[0];

    // Calculate expiry time based on selected plan
    let expiryDate: string;
    let planName = "";
    
    if (selectedPlan === "free") {
      expiryDate = "Unlimited";
      planName = "Free"; // حفظ اسم الخطة المجانية
    } else {
      const plan = pricingPlans.find(p => p.id === selectedPlan);
      if (plan) {
        planName = plan.name_plan;
        const monthsToAdd = parseInt(plan.duration_months || "1");
        const baseDate = new Date(startDate);
        baseDate.setMonth(baseDate.getMonth() + monthsToAdd);
        expiryDate = baseDate.toISOString().split('T')[0];
      } else {
        expiryDate = "Unlimited";
        planName = "Free";
      }
    }

    const formattedCredits = credits + ".0";
    const newUser = {
      Name: name,
      Email: email,
      Password: password,
      Credits: formattedCredits,
      User_Type: userType,
      Phone: phone,
      Country: country,
      Activate: "Active",
      Block: "Not Blocked",
      Start_Date: startDate,
      Expiry_Time: expiryDate,
      Email_Type: "User",
      Hwid: "Null",
      My_Plans: planName // حفظ اسم الخطة دائماً، حتى للخطة المجانية
    };
    
    try {
      setLoading(true);

      const success = await onSave(newUser);
      
      if (success === true) {
        // Reset form fields with new random data
        setName(generateRandomName());
        setEmail("");
        setPassword("");
        setCredits("0");
        setUserType("Credits License");
        setPhone(generateRandomPhone());
        setCountry("Saudi Arabia");
        setSubscriptionMonths("3");
        setSelectedPlan("free");
        
        // Close the dialog
        onClose();
      }
    } catch (error) {
      console.log("Error in handleSubmit:", error);
      toast(t("error") || "خطأ", {
        description: t("addUserError") || "فشل في إضافة المستخدم"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent dir={isRTL ? "rtl" : "ltr"} className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("addNewUser") || "إضافة مستخدم جديد"}</DialogTitle>
          <DialogDescription>
            {t("enterNewUserData") || "أدخل بيانات المستخدم الجديد"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">{t("name") || "الاسم"}</Label>
              <Input id="name" value={name} onChange={e => setName(e.target.value)} required />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="email">{t("email") || "البريد الإلكتروني"}</Label>
              <Input id="email" type="email" value={email} onChange={e => setEmail(e.target.value)} required />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="password">{t("password") || "كلمة المرور"}</Label>
              <Input id="password" type="password" value={password} onChange={e => setPassword(e.target.value)} required />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="plan">{t("plan") || "الخطة"}</Label>
              <Select value={selectedPlan} onValueChange={setSelectedPlan} disabled={plansLoading}>
                <SelectTrigger id="plan">
                  <SelectValue placeholder={t("selectPlan") || "اختر الخطة"} />
                </SelectTrigger>
                <SelectContent>
                  {pricingPlans.map(plan => (
                    <SelectItem key={plan.id} value={plan.id}>
                      {plan.name_plan} {plan.id !== 'free' && `- $${plan.price}`}
                      {plan.duration_months && parseInt(plan.duration_months) > 0 && 
                        ` (${plan.duration_months} ${parseInt(plan.duration_months) === 1 ? (t("month") || "شهر") : (t("months") || "أشهر")})`
                      }
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedPlan === "free" && (
              <div className="grid gap-2">
                <Label htmlFor="credits">{t("credit") || "الرصيد"}</Label>
                <Input id="credits" type="number" value={credits} onChange={e => setCredits(e.target.value)} required />
              </div>
            )}
            
            <div className="grid gap-2">
              <Label htmlFor="phone">{t("phone") || "رقم الهاتف"}</Label>
              <Input id="phone" value={phone} onChange={e => setPhone(e.target.value)} required />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="country">{t("country") || "الدولة"}</Label>
              <Select value={country} onValueChange={setCountry}>
                <SelectTrigger id="country">
                  <SelectValue placeholder={t("selectCountry") || "اختر الدولة"} />
                </SelectTrigger>
                <SelectContent>
                  {countries.map(countryName => (
                    <SelectItem key={countryName} value={countryName}>
                      {countryName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter className="sm:justify-start">
            <Button type="submit" disabled={loading || plansLoading}>
              {loading ? (t("adding") || "جاري الإضافة...") : (t("addUser") || "إضافة المستخدم")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
