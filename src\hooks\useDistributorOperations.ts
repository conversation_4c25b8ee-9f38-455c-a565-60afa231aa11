import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "./useLanguage";
import { useAuth } from "./auth/AuthContext";
import { useCallback } from "react";

export interface DistributorUser {
  id: string;
  uid: string;
  name: string;
  email: string;
  phone?: string;
  credits: string;
  user_type: string;
  activate: string;
  block: string;
  expiry_time?: string;
  start_date?: string;
  my_plans?: string;
  country?: string;
  created_at: string;
  updated_at?: string;
}

export interface Commission {
  id: string;
  commission_amount: number;
  commission_percentage: number;
  operation_amount: number;
  commission_type: string;
  status: string;
  created_at: string;
  paid_at?: string;
  user: {
    name: string;
    email: string;
  };
  operation?: {
    operation_id: string;
    operation_type: string;
  };
}

export interface SubDistributor {
  id: string;
  auth_user_id: string;
  name: string;
  email: string;
  phone?: string;
  distributor_level: number;
  commission_percentage: number;
  region?: string;
  total_users: number;
  total_commission: number;
  is_active: boolean;
  created_at: string;
}

export interface DistributorStats {
  totalUsers: number;
  activeUsers: number;
  blockedUsers: number;
  totalCommissions: number;
  paidCommissions: number;
  pendingCommissions: number;
  totalOperations: number;
  subDistributors: number;
}

export const useDistributorOperations = () => {
  const { t } = useLanguage();
  const { user } = useAuth();

  const getMyUsers = useCallback(async (): Promise<DistributorUser[]> => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('distributor_id', user.distributorId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching distributor users:', error);
      toast(t("fetchUsersError") || "فشل في جلب بيانات المستخدمين", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return [];
    }
  }, [user?.distributorId, t]);

  const getDistributorStats = useCallback(async (): Promise<DistributorStats> => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      // Get users statistics
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('activate, block')
        .eq('distributor_id', user.distributorId);

      if (usersError) throw usersError;

      // Get commissions statistics
      const { data: commissions, error: commissionsError } = await supabase
        .from('distributor_commissions')
        .select('commission_amount, status')
        .eq('distributor_id', user.distributorId);

      if (commissionsError) throw commissionsError;

      // Get sub-distributors count
      const { data: subDistributors, error: subDistributorsError } = await supabase
        .from('distributors')
        .select('id')
        .eq('parent_distributor_id', user.distributorId);

      if (subDistributorsError) throw subDistributorsError;

      // Calculate statistics
      const totalUsers = users?.length || 0;
      const activeUsers = users?.filter(u => u.activate === 'Active').length || 0;
      const blockedUsers = users?.filter(u => u.block === 'Blocked').length || 0;

      const totalCommissions = commissions?.reduce((sum, c) => sum + c.commission_amount, 0) || 0;
      const paidCommissions = commissions?.filter(c => c.status === 'paid').reduce((sum, c) => sum + c.commission_amount, 0) || 0;
      const pendingCommissions = commissions?.filter(c => c.status === 'pending').reduce((sum, c) => sum + c.commission_amount, 0) || 0;

      return {
        totalUsers,
        activeUsers,
        blockedUsers,
        totalCommissions,
        paidCommissions,
        pendingCommissions,
        totalOperations: commissions?.length || 0,
        subDistributors: subDistributors?.length || 0
      };
    } catch (error) {
      console.error('Error fetching distributor stats:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        blockedUsers: 0,
        totalCommissions: 0,
        paidCommissions: 0,
        pendingCommissions: 0,
        totalOperations: 0,
        subDistributors: 0
      };
    }
  }, [user?.distributorId]);

  const getMyCommissions = useCallback(async (): Promise<Commission[]> => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { data, error } = await supabase
        .from('distributor_commissions')
        .select(`
          *,
          user:users!user_id(name, email),
          operation:operations!operation_id(operation_id, operation_type)
        `)
        .eq('distributor_id', user.distributorId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching commissions:', error);
      toast(t("fetchCommissionsError") || "فشل في جلب بيانات العمولات", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return [];
    }
  }, [user?.distributorId, t]);

  const getCommissionSummary = useCallback(async () => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { data, error } = await supabase
        .from('distributor_commissions')
        .select('commission_amount, status')
        .eq('distributor_id', user.distributorId);

      if (error) {
        throw error;
      }

      const summary = {
        totalCommissions: 0,
        paidCommissions: 0,
        pendingCommissions: 0,
        totalOperations: data?.length || 0
      };

      data?.forEach(commission => {
        summary.totalCommissions += commission.commission_amount;
        if (commission.status === 'paid') {
          summary.paidCommissions += commission.commission_amount;
        } else if (commission.status === 'pending') {
          summary.pendingCommissions += commission.commission_amount;
        }
      });

      return summary;
    } catch (error) {
      console.error('Error fetching commission summary:', error);
      return {
        totalCommissions: 0,
        paidCommissions: 0,
        pendingCommissions: 0,
        totalOperations: 0
      };
    }
  }, [user?.distributorId]);

  const getSubDistributors = useCallback(async (): Promise<SubDistributor[]> => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { data, error } = await supabase
        .from('distributors')
        .select('*')
        .eq('parent_distributor_id', user.distributorId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching sub-distributors:', error);
      toast(t("fetchSubDistributorsError") || "فشل في جلب بيانات الموزعين الفرعيين", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return [];
    }
  }, [user?.distributorId, t]);

  const updateUserStatus = async (userId: string, status: 'Active' | 'Inactive') => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { error } = await supabase
        .from('users')
        .update({
          activate: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .eq('distributor_id', user.distributorId);

      if (error) {
        throw error;
      }

      toast(t("userStatusUpdated") || "تم تحديث حالة المستخدم", {
        description: status === 'Active' ? t("userActivated") || "تم تفعيل المستخدم" : t("userDeactivated") || "تم إلغاء تفعيل المستخدم"
      });

      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      toast(t("updateUserStatusError") || "فشل في تحديث حالة المستخدم", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  const blockUser = async (userId: string, block: 'Blocked' | 'Not Blocked') => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      const { error } = await supabase
        .from('users')
        .update({
          block,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .eq('distributor_id', user.distributorId);

      if (error) {
        throw error;
      }

      toast(t("userBlockStatusUpdated") || "تم تحديث حالة حظر المستخدم", {
        description: block === 'Blocked' ? t("userBlocked") || "تم حظر المستخدم" : t("userUnblocked") || "تم إلغاء حظر المستخدم"
      });

      return true;
    } catch (error) {
      console.error('Error updating user block status:', error);
      toast(t("updateUserBlockError") || "فشل في تحديث حالة حظر المستخدم", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  const createSubDistributor = async (distributorData: {
    email: string;
    password: string;
    name: string;
    phone?: string;
    region?: string;
    commissionPercentage: number;
    maxSubDistributors: number;
    canCreateDistributors: boolean;
  }) => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      if (!user.canCreateDistributors) {
        throw new Error('You do not have permission to create sub-distributors');
      }

      // 1. Create user in auth.users using Edge Function
      const { data: result, error: createError } = await supabase.functions.invoke('create-user', {
        body: {
          email: distributorData.email,
          password: distributorData.password,
          name: distributorData.name,
          phone: distributorData.phone
        }
      });

      if (createError) {
        throw new Error(`Failed to create user: ${createError.message}`);
      }

      const userId = result.userId;

      // 2. Create distributor record
      const { error: distributorError } = await supabase
        .from('distributors')
        .insert({
          auth_user_id: userId,
          name: distributorData.name,
          email: distributorData.email,
          phone: distributorData.phone || null,
          parent_distributor_id: user.distributorId,
          distributor_level: (user.distributorLevel || 1) + 1,
          commission_percentage: distributorData.commissionPercentage.toString(),
          region: distributorData.region || null,
          max_sub_distributors: distributorData.maxSubDistributors,
          can_create_distributors: distributorData.canCreateDistributors,
          total_users: 0,
          total_commission: "0.00",
          is_active: true
        });

      if (distributorError) {
        // If distributor creation fails, try to delete the auth user
        await supabase.auth.admin.deleteUser(userId);
        throw new Error(`Failed to create distributor: ${distributorError.message}`);
      }

      // 3. Add to account_types table
      const { error: accountTypeError } = await supabase
        .from('account_types')
        .insert({
          auth_user_id: userId,
          account_type: 'distributor'
        });

      if (accountTypeError) {
        console.error('Error adding to account_types:', accountTypeError);
        // Don't fail the whole operation for this
      }

      toast(t("subDistributorCreatedSuccess") || "تم إنشاء الموزع الفرعي بنجاح", {
        description: t("subDistributorCreatedDescription") || "تم إنشاء حساب الموزع الفرعي وإرسال بيانات الدخول"
      });

      return { success: true, userId };
    } catch (error) {
      console.error('Error creating sub-distributor:', error);
      toast(t("subDistributorCreationFailed") || "فشل في إنشاء الموزع الفرعي", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      throw error;
    }
  };

  const calculateCommission = (operationAmount: number, commissionPercentage: number) => {
    return (operationAmount * commissionPercentage) / 100;
  };

  const addCommission = async (userId: string, operationId: string, operationAmount: number) => {
    try {
      if (!user?.distributorId || !user.commissionPercentage) {
        throw new Error('Distributor information not found');
      }

      const commissionAmount = calculateCommission(operationAmount, user.commissionPercentage);

      const { error } = await supabase
        .from('distributor_commissions')
        .insert({
          distributor_id: user.distributorId,
          user_id: userId,
          operation_id: operationId,
          commission_amount: commissionAmount,
          commission_percentage: user.commissionPercentage,
          operation_amount: operationAmount,
          commission_type: 'operation',
          status: 'pending'
        });

      if (error) {
        throw error;
      }

      // Update distributor's total commission
      const { error: updateError } = await supabase
        .from('distributors')
        .update({
          total_commission: supabase.sql`total_commission + ${commissionAmount}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.distributorId);

      if (updateError) {
        console.error('Error updating distributor total commission:', updateError);
      }

      return true;
    } catch (error) {
      console.error('Error adding commission:', error);
      return false;
    }
  };

  const createUser = async (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    country?: string;
    userType: string;
    credits?: string;
    expiryTime?: string;
  }) => {
    try {
      if (!user?.distributorId) {
        throw new Error('Distributor ID not found');
      }

      // 1. Create user in auth.users using Edge Function
      const { data: result, error: createError } = await supabase.functions.invoke('create-user', {
        body: {
          email: userData.email,
          password: userData.password,
          name: userData.name,
          phone: userData.phone
        }
      });

      if (createError) {
        throw new Error(`Failed to create user: ${createError.message}`);
      }

      const userId = result.userId;

      // 2. Add user data to public.users table with distributor assignment
      const { error: userError } = await supabase.from('users').insert({
        id: userId,
        uid: userId,
        email: userData.email,
        password: userData.password,
        name: userData.name || '',
        phone: userData.phone || '',
        country: userData.country || '',
        activate: 'Active',
        block: 'Not Blocked',
        credits: userData.credits || '0.0',
        user_type: userData.userType,
        expiry_time: userData.expiryTime,
        start_date: new Date().toISOString().split('T')[0],
        hwid: 'Null',
        my_plans: null,
        distributor_id: user.distributorId, // Assign to current distributor
        assigned_by_admin: null
      });

      if (userError) {
        // If user creation fails, try to delete the auth user
        await supabase.auth.admin.deleteUser(userId);
        throw new Error(`Failed to add user data: ${userError.message}`);
      }

      // 3. Update distributor's total users count
      const { error: updateError } = await supabase
        .from('distributors')
        .update({
          total_users: supabase.sql`total_users + 1`,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.distributorId);

      if (updateError) {
        console.error('Error updating distributor total users:', updateError);
      }

      toast(t("userCreatedSuccess") || "تم إنشاء المستخدم بنجاح", {
        description: t("userCreatedDescription") || "تم إنشاء المستخدم وتعيينه لك"
      });

      return { success: true, userId };
    } catch (error) {
      console.error('Error creating user:', error);
      toast(t("userCreationFailed") || "فشل في إنشاء المستخدم", {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      throw error;
    }
  };

  return {
    getMyUsers,
    getMyCommissions,
    getCommissionSummary,
    getSubDistributors,
    getDistributorStats,
    createSubDistributor,
    updateUserStatus,
    blockUser,
    calculateCommission,
    addCommission,
    createUser
  };
};
