import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from "@/hooks/useLanguage";
import { useDistributorOperations } from "@/hooks/useDistributorOperations";
import { toast } from "@/components/ui/sonner";
import { Loader2, User, Mail, Phone, MapPin, Percent, Users } from "lucide-react";

interface AddSubDistributorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  email: string;
  password: string;
  phone: string;
  region: string;
  commissionPercentage: number;
  maxSubDistributors: number;
  canCreateDistributors: boolean;
}

export function AddSubDistributorDialog({ isOpen, onClose, onSuccess }: AddSubDistributorDialogProps) {
  const { t } = useLanguage();
  const { createSubDistributor } = useDistributorOperations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    phone: "",
    region: "",
    commissionPercentage: 5, // Lower than parent
    maxSubDistributors: 3,
    canCreateDistributors: false // Default to false for sub-distributors
  });

  const handleInputChange = (field: keyof FormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast.error(t("nameRequired") || "الاسم مطلوب");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error(t("emailRequired") || "البريد الإلكتروني مطلوب");
      return false;
    }
    if (!formData.password || formData.password.length < 8) {
      toast.error(t("passwordMinLength") || "كلمة المرور يجب أن تكون 8 أحرف على الأقل");
      return false;
    }
    if (formData.commissionPercentage < 0 || formData.commissionPercentage > 50) {
      toast.error(t("invalidCommissionPercentage") || "نسبة العمولة غير صحيحة");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      await createSubDistributor({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        phone: formData.phone,
        region: formData.region,
        commissionPercentage: formData.commissionPercentage,
        maxSubDistributors: formData.maxSubDistributors,
        canCreateDistributors: formData.canCreateDistributors
      });

      onSuccess();
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        password: "",
        phone: "",
        region: "",
        commissionPercentage: 5,
        maxSubDistributors: 3,
        canCreateDistributors: false
      });

    } catch (error) {
      // Error is already handled in createSubDistributor function
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("addSubDistributor") || "إضافة موزع فرعي"}</DialogTitle>
          <DialogDescription>
            {t("addSubDistributorDescription") || "إضافة موزع فرعي جديد تحت إدارتك"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("basicInformation") || "المعلومات الأساسية"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  <User className="inline mr-1 h-4 w-4" />
                  {t("fullName") || "الاسم الكامل"} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t("enterFullName") || "أدخل الاسم الكامل"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  <Mail className="inline mr-1 h-4 w-4" />
                  {t("email") || "البريد الإلكتروني"} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"}
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">
                  {t("password") || "كلمة المرور"} *
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder={t("enterPassword") || "أدخل كلمة المرور"}
                  minLength={8}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  <Phone className="inline mr-1 h-4 w-4" />
                  {t("phone") || "رقم الهاتف"}
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t("enterPhone") || "أدخل رقم الهاتف"}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="region">
                <MapPin className="inline mr-1 h-4 w-4" />
                {t("region") || "المنطقة"}
              </Label>
              <Input
                id="region"
                value={formData.region}
                onChange={(e) => handleInputChange('region', e.target.value)}
                placeholder={t("enterRegion") || "أدخل المنطقة"}
              />
            </div>
          </div>

          {/* Distributor Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("distributorSettings") || "إعدادات الموزع"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="commissionPercentage">
                  <Percent className="inline mr-1 h-4 w-4" />
                  {t("commissionPercentage") || "نسبة العمولة"} (%)
                </Label>
                <Input
                  id="commissionPercentage"
                  type="number"
                  min="0"
                  max="50"
                  step="0.1"
                  value={formData.commissionPercentage}
                  onChange={(e) => handleInputChange('commissionPercentage', parseFloat(e.target.value) || 0)}
                  placeholder="5.0"
                />
                <p className="text-xs text-muted-foreground">
                  {t("commissionNote") || "يجب أن تكون أقل من نسبة عمولتك"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxSubDistributors">
                  <Users className="inline mr-1 h-4 w-4" />
                  {t("maxSubDistributors") || "الحد الأقصى للموزعين الفرعيين"}
                </Label>
                <Input
                  id="maxSubDistributors"
                  type="number"
                  min="0"
                  max="20"
                  value={formData.maxSubDistributors}
                  onChange={(e) => handleInputChange('maxSubDistributors', parseInt(e.target.value) || 0)}
                  placeholder="3"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="canCreateDistributors">
                {t("canCreateSubDistributors") || "يمكنه إنشاء موزعين فرعيين"}
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="canCreateDistributors"
                  checked={formData.canCreateDistributors}
                  onCheckedChange={(checked) => handleInputChange('canCreateDistributors', checked)}
                />
                <Label htmlFor="canCreateDistributors" className="text-sm text-muted-foreground">
                  {formData.canCreateDistributors 
                    ? t("enabled") || "مفعل" 
                    : t("disabled") || "معطل"
                  }
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                {t("subDistributorNote") || "السماح للموزع الفرعي بإنشاء موزعين فرعيين آخرين"}
              </p>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("cancel") || "إلغاء"}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting 
                ? t("creating") || "جاري الإنشاء..." 
                : t("addSubDistributor") || "إضافة الموزع الفرعي"
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
