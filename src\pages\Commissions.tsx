import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDistributorOperations, type Commission } from "@/hooks/useDistributorOperations";
import { useLanguage } from "@/hooks/useLanguage";
import { 
  DollarSign, 
  Search, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle,
  Calendar,
  User,
  Activity
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

const Commissions = () => {
  const { t } = useLanguage();
  const { getMyCommissions, getCommissionSummary } = useDistributorOperations();
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [filteredCommissions, setFilteredCommissions] = useState<Commission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [summary, setSummary] = useState({
    totalCommissions: 0,
    paidCommissions: 0,
    pendingCommissions: 0,
    totalOperations: 0
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [commissionsData, summaryData] = await Promise.all([
          getMyCommissions(),
          getCommissionSummary()
        ]);
        setCommissions(commissionsData);
        setFilteredCommissions(commissionsData);
        setSummary(summaryData);
      } catch (error) {
        console.error('Error fetching commissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [getMyCommissions, getCommissionSummary]);

  useEffect(() => {
    let filtered = commissions.filter(commission =>
      commission.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (commission.operation?.operation_id || '').toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (statusFilter !== "all") {
      filtered = filtered.filter(commission => commission.status === statusFilter);
    }

    setFilteredCommissions(filtered);
  }, [searchTerm, statusFilter, commissions]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            {t("paid") || "مدفوع"}
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            {t("pending") || "معلق"}
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            {t("cancelled") || "ملغي"}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCommissionTypeBadge = (type: string) => {
    const typeMap = {
      operation: { label: t("operation") || "عملية", color: "bg-blue-100 text-blue-800" },
      subscription: { label: t("subscription") || "اشتراك", color: "bg-purple-100 text-purple-800" },
      bonus: { label: t("bonus") || "مكافأة", color: "bg-orange-100 text-orange-800" }
    };

    const typeInfo = typeMap[type as keyof typeof typeMap] || { label: type, color: "bg-gray-100 text-gray-800" };

    return (
      <Badge variant="outline" className={typeInfo.color}>
        {typeInfo.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
              </CardHeader>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {t("commissions") || "العمولات"}
        </h1>
        <p className="text-muted-foreground">
          {t("viewCommissionHistory") || "عرض تاريخ العمولات والأرباح"}
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalCommissions") || "إجمالي العمولات"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(summary.totalCommissions)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("paidCommissions") || "العمولات المدفوعة"}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(summary.paidCommissions)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("pendingCommissions") || "العمولات المعلقة"}
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {formatCurrency(summary.pendingCommissions)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("totalOperations") || "إجمالي العمليات"}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalOperations}</div>
          </CardContent>
        </Card>
      </div>

      {/* Commissions Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {t("commissionHistory") || "تاريخ العمولات"}
          </CardTitle>
          <CardDescription>
            {t("totalCommissions") || "إجمالي العمولات"}: {commissions.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t("searchCommissions") || "البحث في العمولات..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("filterByStatus") || "تصفية حسب الحالة"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allStatuses") || "جميع الحالات"}</SelectItem>
                <SelectItem value="pending">{t("pending") || "معلق"}</SelectItem>
                <SelectItem value="paid">{t("paid") || "مدفوع"}</SelectItem>
                <SelectItem value="cancelled">{t("cancelled") || "ملغي"}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("user") || "المستخدم"}</TableHead>
                  <TableHead>{t("operationId") || "معرف العملية"}</TableHead>
                  <TableHead>{t("type") || "النوع"}</TableHead>
                  <TableHead>{t("operationAmount") || "مبلغ العملية"}</TableHead>
                  <TableHead>{t("commissionRate") || "معدل العمولة"}</TableHead>
                  <TableHead>{t("commissionAmount") || "مبلغ العمولة"}</TableHead>
                  <TableHead>{t("status") || "الحالة"}</TableHead>
                  <TableHead>{t("date") || "التاريخ"}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCommissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <DollarSign className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {searchTerm || statusFilter !== "all"
                            ? t("noCommissionsFound") || "لم يتم العثور على عمولات"
                            : t("noCommissionsYet") || "لا توجد عمولات بعد"
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCommissions.map((commission) => (
                    <TableRow key={commission.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{commission.user.name}</div>
                            <div className="text-sm text-muted-foreground">{commission.user.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {commission.operation?.operation_id || '-'}
                      </TableCell>
                      <TableCell>
                        {getCommissionTypeBadge(commission.commission_type)}
                      </TableCell>
                      <TableCell className="font-mono">
                        {formatCurrency(commission.operation_amount)}
                      </TableCell>
                      <TableCell className="font-mono">
                        {commission.commission_percentage}%
                      </TableCell>
                      <TableCell className="font-mono font-semibold">
                        {formatCurrency(commission.commission_amount)}
                      </TableCell>
                      <TableCell>{getStatusBadge(commission.status)}</TableCell>
                      <TableCell className="text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(commission.created_at)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Commissions;
