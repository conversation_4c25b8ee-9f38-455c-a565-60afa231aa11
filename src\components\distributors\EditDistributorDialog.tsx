import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from "@/hooks/useLanguage";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { Loader2, User, Mail, Phone, MapPin, Percent, Users } from "lucide-react";

interface Distributor {
  id: string;
  auth_user_id: string;
  name: string;
  email: string;
  phone?: string;
  parent_distributor_id?: string;
  distributor_level: number;
  commission_percentage: string;
  region?: string;
  max_sub_distributors: number;
  can_create_distributors: boolean;
  total_users: number;
  total_commission: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface EditDistributorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  distributor: Distributor | null;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  email: string;
  phone: string;
  region: string;
  commissionPercentage: number;
  maxSubDistributors: number;
  canCreateDistributors: boolean;
  distributorLevel: number;
  isActive: boolean;
}

export function EditDistributorDialog({ isOpen, onClose, distributor, onSuccess }: EditDistributorDialogProps) {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    region: "",
    commissionPercentage: 10,
    maxSubDistributors: 5,
    canCreateDistributors: true,
    distributorLevel: 1,
    isActive: true
  });

  // Update form data when distributor changes
  useEffect(() => {
    if (distributor) {
      setFormData({
        name: distributor.name,
        email: distributor.email,
        phone: distributor.phone || "",
        region: distributor.region || "",
        commissionPercentage: parseFloat(distributor.commission_percentage),
        maxSubDistributors: distributor.max_sub_distributors,
        canCreateDistributors: distributor.can_create_distributors,
        distributorLevel: distributor.distributor_level,
        isActive: distributor.is_active
      });
    }
  }, [distributor]);

  const handleInputChange = (field: keyof FormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast.error(t("nameRequired") || "الاسم مطلوب");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error(t("emailRequired") || "البريد الإلكتروني مطلوب");
      return false;
    }
    if (formData.commissionPercentage < 0 || formData.commissionPercentage > 100) {
      toast.error(t("invalidCommissionPercentage") || "نسبة العمولة غير صحيحة");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!distributor || !validateForm()) return;

    try {
      setIsSubmitting(true);

      // Update distributor record
      const { error: distributorError } = await supabase
        .from('distributors')
        .update({
          name: formData.name,
          email: formData.email,
          phone: formData.phone || null,
          region: formData.region || null,
          commission_percentage: formData.commissionPercentage.toString(),
          max_sub_distributors: formData.maxSubDistributors,
          can_create_distributors: formData.canCreateDistributors,
          distributor_level: formData.distributorLevel,
          is_active: formData.isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', distributor.id);

      if (distributorError) {
        throw new Error(`Failed to update distributor: ${distributorError.message}`);
      }

      // Update auth user email if changed
      if (formData.email !== distributor.email) {
        const { error: authError } = await supabase.auth.admin.updateUserById(
          distributor.auth_user_id,
          { email: formData.email }
        );

        if (authError) {
          console.error('Error updating auth user email:', authError);
          // Don't fail the whole operation for this
        }
      }

      toast.success(t("distributorUpdatedSuccess") || "تم تحديث الموزع بنجاح");
      onSuccess();

    } catch (error) {
      console.error('Error updating distributor:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : t("distributorUpdateFailed") || "فشل في تحديث الموزع"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!distributor) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("editDistributor") || "تعديل الموزع"}</DialogTitle>
          <DialogDescription>
            {t("editDistributorDescription") || "تعديل بيانات وإعدادات الموزع"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("basicInformation") || "المعلومات الأساسية"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  <User className="inline mr-1 h-4 w-4" />
                  {t("fullName") || "الاسم الكامل"} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t("enterFullName") || "أدخل الاسم الكامل"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  <Mail className="inline mr-1 h-4 w-4" />
                  {t("email") || "البريد الإلكتروني"} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"}
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="phone">
                  <Phone className="inline mr-1 h-4 w-4" />
                  {t("phone") || "رقم الهاتف"}
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t("enterPhone") || "أدخل رقم الهاتف"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="region">
                  <MapPin className="inline mr-1 h-4 w-4" />
                  {t("region") || "المنطقة"}
                </Label>
                <Input
                  id="region"
                  value={formData.region}
                  onChange={(e) => handleInputChange('region', e.target.value)}
                  placeholder={t("enterRegion") || "أدخل المنطقة"}
                />
              </div>
            </div>
          </div>

          {/* Distributor Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("distributorSettings") || "إعدادات الموزع"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="commissionPercentage">
                  <Percent className="inline mr-1 h-4 w-4" />
                  {t("commissionPercentage") || "نسبة العمولة"} (%)
                </Label>
                <Input
                  id="commissionPercentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.commissionPercentage}
                  onChange={(e) => handleInputChange('commissionPercentage', parseFloat(e.target.value) || 0)}
                  placeholder="10.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="distributorLevel">
                  {t("distributorLevel") || "مستوى الموزع"}
                </Label>
                <Input
                  id="distributorLevel"
                  type="number"
                  min="1"
                  max="5"
                  value={formData.distributorLevel}
                  onChange={(e) => handleInputChange('distributorLevel', parseInt(e.target.value) || 1)}
                  placeholder="1"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="maxSubDistributors">
                  <Users className="inline mr-1 h-4 w-4" />
                  {t("maxSubDistributors") || "الحد الأقصى للموزعين الفرعيين"}
                </Label>
                <Input
                  id="maxSubDistributors"
                  type="number"
                  min="0"
                  max="50"
                  value={formData.maxSubDistributors}
                  onChange={(e) => handleInputChange('maxSubDistributors', parseInt(e.target.value) || 0)}
                  placeholder="5"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="canCreateDistributors">
                  {t("canCreateSubDistributors") || "يمكنه إنشاء موزعين فرعيين"}
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="canCreateDistributors"
                    checked={formData.canCreateDistributors}
                    onCheckedChange={(checked) => handleInputChange('canCreateDistributors', checked)}
                  />
                  <Label htmlFor="canCreateDistributors" className="text-sm text-muted-foreground">
                    {formData.canCreateDistributors 
                      ? t("enabled") || "مفعل" 
                      : t("disabled") || "معطل"
                    }
                  </Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive">
                {t("accountStatus") || "حالة الحساب"}
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                />
                <Label htmlFor="isActive" className="text-sm text-muted-foreground">
                  {formData.isActive 
                    ? t("active") || "نشط" 
                    : t("inactive") || "غير نشط"
                  }
                </Label>
              </div>
            </div>
          </div>

          {/* Statistics (Read-only) */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("statistics") || "الإحصائيات"}</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label>{t("totalUsers") || "إجمالي المستخدمين"}</Label>
                <div className="p-2 bg-muted rounded-md">
                  {distributor.total_users}
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t("totalCommissions") || "إجمالي العمولات"}</Label>
                <div className="p-2 bg-muted rounded-md">
                  {parseFloat(distributor.total_commission).toFixed(2)}
                </div>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label>{t("joinDate") || "تاريخ الانضمام"}</Label>
                <div className="p-2 bg-muted rounded-md">
                  {new Date(distributor.created_at).toLocaleDateString()}
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t("lastUpdate") || "آخر تحديث"}</Label>
                <div className="p-2 bg-muted rounded-md">
                  {distributor.updated_at 
                    ? new Date(distributor.updated_at).toLocaleDateString()
                    : t("never") || "أبداً"
                  }
                </div>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("cancel") || "إلغاء"}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting 
                ? t("updating") || "جاري التحديث..." 
                : t("updateDistributor") || "تحديث الموزع"
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
