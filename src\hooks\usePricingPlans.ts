
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export function usePricingPlans() {
  return useQuery({
    queryKey: ['pricingPlans'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pricing')
        .select('*')
        .order('name_plan');

      if (error) {
        throw error;
      }

      // Add the "Free" plan as the first option
      const freePlan = {
        id: 'free',
        name_plan: 'Free',
        price: '0',
        features: '',
        perks: '',
        duration_months: '0'
      };

      return [freePlan, ...(data || [])];
    }
  });
}
